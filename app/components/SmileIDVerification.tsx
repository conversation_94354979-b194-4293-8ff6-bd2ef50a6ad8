import React, { useState, useRef, useEffect } from "react";
import { View, StyleSheet, ActivityIndicator } from "react-native";
import { WebView } from "react-native-webview";
import { colors } from "../config/colors";

// Define the types of verification
export type VerificationType = "selfie" | "document";

// Define the props for the component
interface SmileIDVerificationProps {
  // Required props
  verificationType: VerificationType;
  onComplete: (result: any) => void;
  onError?: (error: any) => void;
  onCancel?: () => void;

  // Optional props
  documentType?: string;
  countryCode?: string;
  baseUrl?: string;
}

const SmileIDVerification: React.FC<SmileIDVerificationProps> = ({
  verificationType,
  onComplete,
  onError,
  onCancel,
  documentType = "passport",
  countryCode = "NG",
  baseUrl = "https://smile-id-web.vercel.app/", // Replace with your actual URL
}) => {
  const [loading, setLoading] = useState(true);
  const webViewRef = useRef<WebView>(null);

  // Build the URL with query parameters
  const url = `${baseUrl}?type=${verificationType}`;

  // Handle messages from the WebView
  const handleMessage = (event: any) => {
    try {
      const message = JSON.parse(event.nativeEvent.data);
      switch (message.type) {
        case "captureComplete":
          setLoading(false);
          onComplete(message.data);

          break;
        case "cancelled":
          setLoading(false);
          onCancel();
          break;
        case "error":
          setLoading(false);
          onError(message.data);
          break;
        case "loaded":
          setLoading(false);
          break;
        default:
      }
    } catch (error) {
      console.error("Error parsing message from WebView:", error);
      onError({ message: "Failed to parse WebView message" });
    }
  };

  return (
    <View style={styles.container}>
      <WebView
        ref={webViewRef}
        source={{ uri: url }}
        style={styles.webView}
        originWhitelist={["*"]}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        onMessage={handleMessage}
        onError={(error) => {
          console.error("WebView error:", error);
          onError({
            message: "WebView error: " + error.nativeEvent.description,
          });
        }}
        onHttpError={(e) => {
          console.error("WebView HTTP error:", e.nativeEvent);
          onError({ message: "HTTP error: " + e.nativeEvent.statusCode });
        }}
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        allowsFullscreenVideo={true}
      />
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  webView: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default SmileIDVerification;
