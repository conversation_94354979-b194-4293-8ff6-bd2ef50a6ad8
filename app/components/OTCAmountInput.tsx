import React from "react";
import { Dimensions, StyleSheet, TouchableOpacity, View, ViewStyle } from "react-native";
import { colors } from "../config/colors";
import P from "./P";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";

const { width, height } = Dimensions.get("window");
const baseHeight = 812;

interface OTCAmountInputProps {
  amountValue?: React.ReactNode;
  convertedValue?: React.ReactNode;
  balance?: string;
  rate?: string;
  error?: boolean;
  headerText?: string;
  onTogglePress?: () => void;
  toggleStyle?: ViewStyle;
}

export default function OTCAmountInput({
  amountValue,
  convertedValue,
  balance,
  rate,
  error,
  headerText = "How much do you want to send",
  onTogglePress,
  toggleStyle,
}: OTCAmountInputProps) {
  return (
    <View style={styles.container}>
      <View>
        <P style={styles.headerText}>{headerText}</P>
        <View style={styles.amountContainer}>{amountValue}</View>
        <View
          style={[
            styles.line,
            { backgroundColor: error ? colors.red : colors.stroke },
          ]}
        />
        <View style={styles.convertedContainer}>{convertedValue}</View>
        <TouchableOpacity
          style={[styles.toggleButton, toggleStyle]}
          onPress={onTogglePress}
        >
          <SvgXml xml={svg.upDown} />
        </TouchableOpacity>
      </View>

      {/* Balance and Rate Info */}
      {(balance || rate) && (
        <View style={styles.infoContainer}>
          {balance && (
            <View style={styles.infoItem}>
              <SvgXml
                xml={svg.coin1}
                width={16}
                height={16}
                style={styles.infoIcon}
              />
              <P style={styles.infoText}>Available balance: {balance}</P>
            </View>
          )}

          {rate && (
            <View style={styles.infoItem}>
              <SvgXml
                xml={svg.rate}
                width={16}
                height={16}
                style={styles.infoIcon}
              />
              <P style={styles.infoText}>Exchange rate: {rate}</P>
            </View>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 24,
    position: "relative",
  },
  headerText: {
    fontSize: 12,
    lineHeight: 19.2,
    color: colors.textBlack,
    fontFamily: fonts.poppinsRegular,
  },
  amountContainer: {
    width: "90%",
    flexDirection: "row",
    paddingTop: 4,
    paddingBottom: 1,
  },
  line: {
    width: "90%",
    height: 1,
    backgroundColor: colors.stroke,
    // marginVertical: 8,
  },
  convertedContainer: {
    width: "100%",
    flexDirection: "row",
    paddingTop: 4,
  },
  toggleButton: {
    position: "absolute",
    right: 0,
    top: "55%",
    width: 32,
    height: 32,
    alignItems: "center",
    justifyContent: "center",
  },
  infoContainer: {
    marginTop: 16,
    width: "100%",
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  infoIcon: {
    marginRight: 8,
  },
  infoText: {
    fontSize: 12,
    lineHeight: 19.2,
    color: colors.textBlack,
    fontFamily: fonts.poppinsRegular,
  },
});
