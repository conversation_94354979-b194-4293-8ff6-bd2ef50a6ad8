import React, { useContext } from "react";
import { View, Dimensions, Image, Linking, ScrollView } from "react-native";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { CommonActions } from "@react-navigation/native";

import { fonts } from "../config/Fonts";
import P from "./P";
import Button from "./Button";
import BottomSheet from "./BottomSheet";
import { colors } from "../config/colors";
import Link from "./Link";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";
import { navigationRef } from "../navigation/navigationRef";

const { width, height } = Dimensions.get("window");

interface GlobalErrorBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onExploreOptions: () => void;
  errorMessage?: string;
}

const GlobalErrorBottomSheet: React.FC<GlobalErrorBottomSheetProps> = ({
  isVisible,
  onClose,
  onExploreOptions,
  errorMessage,
}) => {
  const { storedCredentails } = useContext(CredentailsContext);

  const handleOkayPress = () => {
    onClose();
    if (navigationRef.isReady()) {
      navigationRef.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: "BottomTabNavigator"}],
        })
      );
    }
  };

  return (
    <BottomSheet
      isVisible={isVisible}
      showBackArrow={false}
      backspaceText=""
      onClose={onClose}
      modalContentStyle={{ height: "55%" }}
      extraModalStyle={{ height: "53%" }}
      components={
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 100 }}>
        <View
          style={{
            width: "100%",
            alignItems: "center",
            justifyContent: "center",
            paddingTop: 34,
          }}
        >
          {/* <SvgXml xml={svg.noCloud} width={44} height={44} /> */}
          <Image source={require("../assets/cancel-circle.png")} style={{ width: 50, height: 50 }} />
          <P style={{ marginTop: 16 }}>Payment process failed!</P>
          <P
            style={{
              textAlign: "center",
              fontSize: 12,
              color: colors.gray,
              marginTop: 4,
              fontFamily: fonts.poppinsRegular,
            }}
          >
            We couldn't complete your payment process{errorMessage ? `. Actual error "${errorMessage}"` : ""}. Please try again, explore other payment options, or contact our support team for assistance.
          </P>
          <View style={{ marginTop: 32, width: "80%" }}>
            <Button
              btnText="Okay"
              onPress={handleOkayPress}
            />
            <Link style={{ marginTop: 16, textAlign: "center" }} onPress={() => {
              const username = storedCredentails?.user?.username || "Not provided";
              const email = storedCredentails?.user?.email || "Not provided";
              const message = `Hi, Support, I have an issue that requires resolving.\nMy Username is ${username} and My email is ${email}`;
              const encodedMessage = encodeURIComponent(message);
              Linking.openURL(
                `https://wa.me/905338563416?text=${encodedMessage}`
              );
            }}>Report issue</Link>
          </View>
        </View>
        </ScrollView>
      }
    />
  );
};

export default GlobalErrorBottomSheet;


