import React, { CSSProperties } from "react";
import { View, Image, StyleSheet, Dimensions } from "react-native";
import P from "./P";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { TouchableOpacity } from "react-native-gesture-handler";

const { width, height } = Dimensions.get("window");
interface PProps {
  flagUri?: any;
  amount?: any;
  date?: any;
  title?: string;
  status?: string;
  statusStyle?: CSSProperties;
  itemStyle?: CSSProperties;
  icon?: any;
  onPress?: any;
  titleStyle?: CSSProperties;
}
export default function TransactionItem({
  flagUri,
  amount,
  date,
  title,
  status,
  statusStyle,
  itemStyle,
  icon,
  onPress,
  titleStyle,
}: PProps) {
  return (
    <TouchableOpacity onPress={onPress}>
      {/* @ts-ignore */}
      <View style={[styles.transactionItem, itemStyle]}>
        <View style={[{ flexDirection: "row", alignItems: "center" }]}>
          {flagUri && <Image source={flagUri} style={styles.transactionFlag} />}
          {icon && <SvgXml xml={icon} />}
          <View style={styles.transactionInfo}>
            {/* @ts-ignore */}
            <P style={[styles.transactionAmount, titleStyle]} numberOfLines={1}>
              {title}
            </P>
            <P style={styles.transactionDate}>{date}</P>
          </View>
        </View>
        <View style={{ position: "absolute", right: 16 }}>
          <P
            style={{
              fontSize: 14,
              fontFamily: "poppins-medium",
            }}
          >
            {amount} <P style={{ fontSize: 10}}>USD</P>
          </P>
          {status && (
            // @ts-ignore
            <P style={[styles.transactionDate, statusStyle]}>{status}</P>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  transactionsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
  },
  transactionItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    // marginBottom: 20,
    paddingTop: 12,
    paddingBottom: 12,
    // backgroundColor: 'red',
    justifyContent: "space-between",
  },
  transactionFlag: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "gray",
  },
  transactionInfo: {
    marginLeft: 10,
  },
  transactionAmount: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    // fontWeight: "bold",
  },
  transactionDate: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
});
