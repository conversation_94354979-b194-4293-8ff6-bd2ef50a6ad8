import React, { CSSProperties } from "react";
import {
  Dimensions,
  ImageBackground,
  Platform,
  StyleSheet,
  View,
} from "react-native";
import { colors } from "../config/colors";
import P from "./P";
import { fonts } from "../config/Fonts";
import { string } from "yup";

interface PProps {
  headText?: any;
  amount?: any;
  convertedAmount?: any;
  bottomComponent?: any;
  timer?: any;
  lineStyle?: CSSProperties;
  image?: any;
  type?: string;
}
const { width, height } = Dimensions.get("window");
const CIRCLE_SIZE = 24;
const GAP = 8;

export default function DetailCard({
  headText,
  amount,
  convertedAmount,
  bottomComponent,
  timer,
  lineStyle,
  image,
  type,
}: PProps) {
  const numCircles = Math.floor(width / (CIRCLE_SIZE + GAP));
  const arr = Array.from({ length: numCircles - 1 });
  return type === "reciept" ? (
    <View style={[styles.cont, {paddingTop: CIRCLE_SIZE, paddingBottom: CIRCLE_SIZE, }]}>
      <View style={[styles.cutCont, { top: -CIRCLE_SIZE / 2 }]}>
        {arr.map((_, idx) => (
          <View key={idx} style={[styles.cutCircle]} />
        ))}
      </View>
      <ImageBackground
        source={require("../assets/RecieptBackground.png")}
        style={{ width: "100%", height: "auto" }}
        resizeMode="cover"
      >
        <View style={{ width: "100%", alignItems: "center" }}>
          {headText && <P style={styles.p1}>{headText}</P>}
          {image}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {amount}
          </View>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {convertedAmount}
          </View>
          {timer}
          {/* @ts-ignore */}
          {Platform.OS === "ios" ? (
            // @ts-ignore
            <View style={[styles.line, lineStyle]} />
          ) : (
            // @ts-ignore
            <View style={[styles.line2, lineStyle]} />
          )}

          <View style={{ width: "100%" }}>{bottomComponent}</View>
        </View>
      </ImageBackground>
      <View style={[styles.cutCont, { bottom: -CIRCLE_SIZE / 2 }]}>
        {arr.map((_, idx) => (
          <View key={idx} style={styles.cutCircle} />
        ))}
      </View>
    </View>
  ) : (
    <View style={styles.cont}>
      <View style={{ width: "100%", alignItems: "center" }}>
        {headText && <P style={styles.p1}>{headText}</P>}
        {image}
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {amount}
        </View>
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {convertedAmount}
        </View>
        {timer}
        {/* @ts-ignore */}
        {Platform.OS === "ios" ? (
          // @ts-ignore
          <View style={[styles.line, lineStyle]} />
        ) : (
          // @ts-ignore
          <View style={[styles.line2, lineStyle]} />
        )}

        <View style={{ width: "100%" }}>{bottomComponent}</View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  cont: {
    width: "100%",
    padding: 16,
    paddingTop: (3.5 * height) / 100,
    paddingBottom: (3.5 * height) / 100,
    backgroundColor: colors.white,
    borderRadius: 12,
    alignItems: "center",
  },
  p1: {
    fontSize: 12,
    color: colors.gray,
    lineHeight: 19.2,
    fontFamily: fonts.poppinsRegular,
  },
  line: {
    width: "100%",
    // height: 1,
    borderWidth: 1,
    borderStyle: "dashed",
    borderColor: colors.stroke,
    marginTop: (3.5 * height) / 100,
    marginBottom: (3.5 * height) / 100,
    // margin: -2,
  },
  line2: {
    width: "100%",
    // height: 1,
    borderBottomWidth: 1,
    borderStyle: "dashed",
    borderColor: colors.stroke,
    marginTop: (3.5 * height) / 100,
    marginBottom: (3.5 * height) / 100,
    // margin: -2,
  },
  cutCont: {
    width: "100%",
    height: CIRCLE_SIZE,
    // backgroundColor: "red",
    position: "absolute",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    overflow: "hidden",
  },
  cutCircle: {
    width: CIRCLE_SIZE,
    height: CIRCLE_SIZE,
    backgroundColor: colors.secBackground,
    borderRadius: 100,
  },
});
