import React, { useEffect, useState, useCallback } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  StatusBar,
  Modal,
  BackHandler,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { colors } from "../config/colors";
import P from "./P";
import { fonts } from "../config/Fonts";
import Button from "./Button";
import Link from "./Link";
import { useNavigation, useFocusEffect, CommonActions } from "@react-navigation/native";
import { GetTransationById } from "../RequestHandlers/Wallet";
import Loader from "./ActivityIndicator";

interface PProps {
  okayPress?: any;
  viewDetailPress?: any;
  tranStat?: "failed" | "success" | "pending";
  visible?: true | false;
  requestClose?: any;
  from?: string;
}
const { width, height } = Dimensions.get("window");
export default function SendMoneyStatus({ navigation, route }) {
  const [stImg, setStImg] = useState(require("../assets/alert-circle.png"));
  const [stText, setStText] = useState("Sent money is pending");
  const [tranStat, setTranState] = useState("pending");
  const { response } = route?.params || {};
  const [tranDetails, setTranDetails] = useState<any>([]);
  const [yellowCardData, setYellowCardData] = useState<any>([]);
  const [loader, setLoader] = useState(false);

  // const [tranStat, setTranStat] = useState("pending");

  const getTransaction = async () => {
    try {
      const id =
        response.id === undefined ? response?.transaction?.id : response?.id;
      const transaction = await GetTransationById(id);      
      if (transaction) {
        setLoader(false);
      }
      if (transaction.transaction) {
        setTranDetails(transaction.transaction);
      }
      if (transaction.yellowCardData) {
        setYellowCardData(transaction.yellowCardData);
      }
      transaction.transaction.status === "completed"
        ? setTranState("success")
        : transaction.transaction.status === "failed"
          ? setTranState("failed")
          : setTranState("pending");
    } catch (error) {
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    if (tranStat === "failed") {
      setStImg(require("../assets/cancel-circle.png"));
      setStText("Sent money failed");
    } else if (tranStat === "success") {
      setStImg(require("../assets/success.png"));
      setStText("Money successfully sent");
    } else {
      setStImg(require("../assets/alert-circle.png"));
    }
  }, [tranStat]);

  useEffect(() => {
    setLoader(true);
    if (response) {
      getTransaction();
    }
    const interval = setInterval(() => {
      getTransaction();
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Handle back navigation
  useFocusEffect(useCallback(() => {
    const onBackPress = () => {
      // Reset navigation stack to avoid loading issues
      // @ts-ignore
      // navigation.navigate("BottomTabNavigator");
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: "BottomTabNavigator" }],
        })
      );
      return true;
    };
    // Disable iOS swipe back gesture
    navigation.setOptions({
      gestureEnabled: false
    });
    // Handle Android back button
    BackHandler.addEventListener("hardwareBackPress", onBackPress);

    return () => {
      BackHandler.removeEventListener("hardwareBackPress", onBackPress);
    };
  }, [navigation]))


  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.body}
    >
      <View style={styles.itemBox}>
        <Image source={stImg} style={{ width: 64, height: 64 }} />
        <P style={styles.statusState}>{stText}</P>
        {tranStat === "failed" ? (
          <P style={styles.stTx}>
            Money sent failed due to technical issue,{"\n"}please try again
            later!
          </P>
        ) : tranStat === "success" ? (
          <P style={styles.stTx}>
            You have successfully sent {tranDetails?.amount} USD to{"\n"}
            {tranDetails?.internalTransferReceiver
              ? `${tranDetails?.internalTransferReceiver?.firstName} ${tranDetails?.internalTransferReceiver?.lastName}`
              : yellowCardData?.destination?.accountName
                ? yellowCardData?.destination?.accountName
                : tranDetails?.toWallet}
          </P>
        ) : (
          <P style={styles.stTx}>
            Money is processing, please check {"\n"}money status later!
          </P>
        )}

        <View style={{ width: "75%", marginTop: 32 }}>
          <Button
            btnText="Okay!"
            onPress={() => {
              setLoader(false);
              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [{ name: "BottomTabNavigator" }],
                })
              );
            }}
          />
          <Link
            style={{ textAlign: "center", marginTop: 16, fontSize: 12 }}
            onPress={() => {
              console.log(tranDetails);
              const transactionType =
                tranDetails?.type === "internal-tranfer"
                  ? "sfx money app"
                  : tranDetails?.paymentGayway === "momo"
                    ? "mobile money"
                    : tranDetails?.paymentGayway === "bank"
                      ? "bank transfer"
                      : tranDetails?.provider === "circle"
                        ? "p2p"
                        : "unknown";
              const id =
                response?.id === undefined
                  ? response?.transaction?.id
                  : response?.id;
              navigation.navigate("AllTransactionDetails", {
                id: id,
                transactionType: transactionType,
              });
            }}
          >
            View details
          </Link>
        </View>
      </View>
      {loader && <Loader />}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    height: (105 * height) / 100,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    bottom: 0,
    // top: 0,
    zIndex: 100,
  },
  itemBox: {
    width: "100%",
    alignItems: "center",
    // marginTop: (20*height)/100
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
});
