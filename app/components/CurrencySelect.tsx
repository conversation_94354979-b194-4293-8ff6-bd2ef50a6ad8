import React, { useState, useEffect } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  TextInput,
} from "react-native";
import P from "./P";
import { svg } from "../config/Svg";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import ListItemSelect from "./ListItemSelect";
import { SvgXml } from "react-native-svg";
import { countries } from "./counties";

interface PProps {
  onPress?: any;
  onActiveCountryChange?: (index: String | null) => void;
  onActiveFlag?: (index: String | null) => void;
  onActiveHomeCountry?: (index: String | null) => void;
  offHeader?: Boolean;
  excludedCountries?: any;
  onCurSymbolChange?: any;
  onCurCodeChange?: any;
  initialSelectedCountry?: string;
  initialSymbol?: string;
  initialCode?: string;
  initialFlag?: string;
  initialHomeCountry?: string;
}

const { width, height } = Dimensions.get("window");

export default function CurrencySelect({
  onPress,
  onActiveCountryChange,
  onActiveFlag,
  offHeader = false,
  excludedCountries,
  onCurSymbolChange,
  onCurCodeChange,
  onActiveHomeCountry,
  initialSelectedCountry = "",
  initialSymbol = "",
  initialCode = "",
  initialFlag = null,
  initialHomeCountry = "",
}: PProps) {
  const [activeType, setActiveType] = useState<number | null>(null);
  const [activeCountry, setActiveCountry] = useState<string>(
    initialSelectedCountry
  );
  const [activeFlag, setActiveFlag] = useState<string | null>(initialFlag);
  const [curSymbol, setCurSymbol] = useState<string>(initialSymbol);
  const [curCode, setCurCode] = useState<string>(initialCode);
  const [homeCountry, setHomeCountry] = useState<string>(initialHomeCountry);
  const [searchQuery, setSearchQuery] = useState<string>("");
  // Array of countries to be excluded
  const filteredCountries = searchQuery
    ? countries
        .filter((country) =>
          country.country.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .filter(
          (country) => !excludedCountries?.includes(country?.country) // Exclude countries
        )
    : countries.filter(
        (country) => !excludedCountries?.includes(country?.country) // Exclude countries
      );

  useEffect(() => {
    if (onActiveCountryChange) {
      onActiveCountryChange(activeCountry);
    }
  }, [activeCountry, onActiveCountryChange]);

  useEffect(() => {
    if (onActiveHomeCountry) {
      onActiveHomeCountry(homeCountry);
    }
  }, [homeCountry, onActiveHomeCountry]);

  useEffect(() => {
    if (onActiveFlag) {
      onActiveFlag(activeFlag);
    }
  }, [activeFlag, onActiveFlag]);

  useEffect(() => {
    if (onCurSymbolChange) {
      onCurSymbolChange(curSymbol);
    }
  }, [curSymbol, onCurSymbolChange]);

  useEffect(() => {
    if (onCurCodeChange) {
      onCurCodeChange(curCode);
    }
  }, [curCode, onCurCodeChange]);
  return (
    <View style={styles.viewContent}>
      <View style={styles.search}>
        <SvgXml xml={svg.search} style={{ marginRight: 8 }} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search country"
          placeholderTextColor={colors.dGray}
          cursorColor={colors.black}
          value={searchQuery}
          onChangeText={(text) => setSearchQuery(text)}
        />
      </View>
      {!offHeader && (
        <P
          style={{
            fontSize: 12,
            color: colors.gray,
            font: fonts.poppinsRegular,
            marginBottom: 8,
          }}
        >
          Select the country local currency you want to
        </P>
      )}

      <ScrollView showsVerticalScrollIndicator={false}>
        {filteredCountries.map((item, index) => (
          <ListItemSelect
            text1={item.currency}
            image={item.flag}
            key={index}
            isActive={activeType === index}
            onPress={() => {
              // Batch all state updates before calling onPress
              const updateStates = () => {
                setActiveType(index);
                setActiveCountry(item.currency);
                setActiveFlag(item.flag);
                setCurSymbol(item.symbol);
                setCurCode(item.currencyCode);
                setHomeCountry(item.country);

                // Delay the onPress callback to ensure all state updates have been processed
                setTimeout(() => {
                  if (onPress) {
                    onPress(index);
                  }
                }, 100);
              };

              // Execute all state updates
              updateStates();
            }}
            containerStyle={{
              marginBottom: index === filteredCountries.length - 1 ? 400 : 16,
            }}
          />
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  viewContent: {
    width: "100%",
    paddingTop: 24,
  },
  search: {
    width: "100%",
    height: (5.5 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    marginBottom: 24,
    borderWidth: 1,
    borderRadius: 99,
    borderColor: colors.stroke,
    padding: 12,
    paddingLeft: 14,
    paddingRight: 14,
    flexDirection: "row",
  },
  searchInput: {
    width: "90%",
    height: 24,
    fontFamily: fonts.poppinsRegular,
    lineHeight: 24,
    fontSize: 14,
  },
});
