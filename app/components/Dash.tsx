import { Dimensions, StyleSheet, View } from "react-native";
import { colors } from "../config/colors";

const { width, height } = Dimensions.get("window");

export default function Dash() {
  return <View style={styles.dash}></View>;
}

const styles = StyleSheet.create({
  dash: {
    width: "100%",
    height: 1,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    marginTop: (2.7 * height) / 100,
  },
});
