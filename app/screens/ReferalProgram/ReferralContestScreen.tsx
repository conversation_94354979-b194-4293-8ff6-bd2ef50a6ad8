import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Dimensions,
  Image,
  StyleSheet,
  View,
  TouchableOpacity,
  ScrollView,
  Platform,
  Share,
  ActivityIndicator,
  FlatList,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import Div from "../../components/Div";
import AuthenticationHedear2 from "../../components/AuthenticationHedear2";
import P from "../../components/P";
import { colors } from "../../config/colors";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import CountdownRing from "../../components/CountdownRing";
import ReferralShare from "../../components/ReferralShare";
import NoteComponent2 from "../../components/NoteComponent2";
import Link from "../../components/Link";
import Button from "../../components/Button";
import BottomSheet from "../../components/BottomSheet";
import BottomSheet2 from "../../components/BottomSheet2";
import * as Clipboard from "expo-clipboard";
import {
  GetReferalCountdown,
  GetReferals,
  GetReferralOverview,
  GetReferralRank,
} from "../../RequestHandlers/Referral";
import { useToast } from "../../context/ToastContext";
import Loader from "../../components/ActivityIndicator";
import { useFocusEffect } from "@react-navigation/native";
import { formatDate } from "../../components/FormatDate";
import { ensureHttps } from "../../components/AddHttp";
import { GetUserDetails } from "../../RequestHandlers/User";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const { width, height } = Dimensions.get("window");

export default function ReferralContestScreen({ navigation, route }) {
  const [totalRef, setTotalRef] = useState(0);
  const [referrals, setReferrals] = useState<any>([]);
  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [referralCode, setReferralCode] = useState(""); // Example referral code
  const referralLink = "tFh273/martins/...sfxchan.co"; // Example referral link
  const [showStep, setShowStep] = useState(false);
  const [referralOverview, setRerralOverview] = useState<any>([]);
  const { handleToast } = useToast();
  const [ranking, setRanking] = useState<any>([]);
  const [rankLoading, setRankLoading] = useState(true);
  const [refLoading, setRefLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const { countdownSeconds } = route.params || 0;
  const [refCode, setRefCode] = useState<any>(null);

  const onShare = async () => {
    try {
      const result = await Share.share({
        message: `invite your friends to signup and join the sfx money app community with your referral code ${referralCode}`,
      });
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error: any) {
      console.error(error.message);
    }
  };

  const copyToClipboard = async (text) => {
    await Clipboard.setStringAsync(text);
  };

  const getReferralRanks = async () => {
    setRankLoading(true);
    try {
      const res = await withApiErrorToast(GetReferralRank(), handleToast);
      if (res.error || res.length === 0) {
        setRanking([]);
      } else {
        setRanking(res);
      }
    } catch (error) {
    } finally {
      setRankLoading(false);
    }
  };

  const getReferralOverview = async () => {
    setLoading(true);
    try {
      const res = await withApiErrorToast(GetReferralOverview(), handleToast);
      if (res.error) {
        handleToast("Error getting overview", "error");
      } else {
        setRerralOverview(res);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getUserDetetails = async () => {
    setLoading(true);
    try {
      const res = await withApiErrorToast(GetUserDetails(), handleToast);
      if (res.referralCode) {
        setReferralCode(res.referralCode);
      } else {
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getReferrals = async () => {
    setRefLoading(true);
    try {
      const res = await GetReferals(1, 3);
      if (res.items) {
        setReferrals(res.items);
      }
    } catch (error) {
    } finally {
      setRefLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      getReferralOverview();
      getReferralRanks();
      getReferrals();
    }, [])
  );

  useEffect(() => {
    getUserDetetails();
  }, []);

  const notes = [
    [
      "To qualify for a top 3 spot, participants must refer a minimum of ",
      <P key="bold" style={{ fontFamily: fonts.poppinsSemibold, fontSize: 11 }}>
        100
      </P>,
      " verified users during the contest period",
    ],
    [
      "Only verified users will be counted towards the contest. Unverified accounts will be excluded from participation and rewards.",
    ],
  ];
  const [activeNoteIndex, setActiveNoteIndex] = useState(0);
  const noteFlatListRef = useRef(null);
  const screenWidth = Dimensions.get("window").width;

  // Infinite auto-scroll logic with extra delay before slide
  useEffect(() => {
    if (notes.length > 1) {
      let timeoutId;
      let intervalId;
      // Add a 1s delay before starting the interval
      timeoutId = setTimeout(() => {
        intervalId = setInterval(() => {
          const nextIndex = (activeNoteIndex + 1) % notes.length;
          setActiveNoteIndex(nextIndex);
          noteFlatListRef.current?.scrollToIndex({
            index: nextIndex,
            animated: true,
          });
        }, 4000);
      }, 1000); // 1s extra delay before starting the interval
      return () => {
        clearTimeout(timeoutId);
        clearInterval(intervalId);
      };
    }
  }, [activeNoteIndex, notes.length]);

  if (loading) {
    return <Loader />;
  }
  return (
    <>
      <LinearGradient
        colors={["#8B52FF", "#F7F4FF"]}
        locations={[0.2, 0.4]}
        style={styles.gradient}
      >
        {/* <Image
          source={require("../../assets/innerImg.png")}
          style={styles.bgImg}
        /> */}
        <View style={styles.ItemBody}>
          <Div>
            <AuthenticationHedear2
              text="Earn & refer"
              iconWhite={true}
              navigation={navigation}
              iconBlack={false}
              textStyle={{ color: colors.white }}
              iconComp={
                <TouchableOpacity
                  onPress={() => navigation.navigate("ContestRuleScreen")}
                >
                  <P style={styles.navText}>Referral rules</P>
                </TouchableOpacity>
              }
            />
            <ScrollView contentContainerStyle={{ paddingBottom: 100 }}>
              <View style={styles.contentBody}>
                <P style={styles.ct_Text}>Referral contest</P>
                <P style={styles.ct_HText}>A chance to win exciting rewards</P>
                <SvgXml xml={svg.trophy} style={{ alignSelf: "center" }} />
                {/* New Referral Contest Section */}
                <View style={styles.contestCard}>
                  <P style={styles.ct_T}>Referral contest reward</P>
                  {/* <View style={styles.contestHeader}>
                    <View>
                      <P style={styles.contestSubtitle}>
                        Join SFx referral contest
                      </P>
                      <P style={styles.contestTitle}>Earning amazing price</P>
                    </View>
                    <SvgXml xml={svg.moneyBag} />
                  </View> */}
                  <View style={styles.referralStatsContainer}>
                    <View style={styles.statItem}>
                      <SvgXml xml={svg.price1} />
                      <P style={styles.statLabel}>1st</P>
                      <P style={styles.statValue}>$300</P>
                    </View>
                    <View style={styles.statItem}>
                      <SvgXml xml={svg.price3} />
                      <P style={styles.statLabel}>2nd</P>
                      <P style={styles.statValue}>$200</P>
                    </View>
                    <View style={styles.statItem}>
                      <SvgXml xml={svg.price2} />
                      <P style={styles.statLabel}>3rd</P>
                      <P style={styles.statValue}>$100</P>
                    </View>
                  </View>
                  <View style={styles.dashedDivider} />
                  <View style={styles.referralStatsContainer}>
                    <View style={styles.statItem}>
                      <P style={styles.statLabel}>Total referral</P>
                      <P style={styles.statValue}>{referralOverview?.total}</P>
                    </View>
                    <View style={styles.statItem}>
                      <P style={styles.statLabel}>Verified</P>
                      <P style={styles.statValue}>
                        {referralOverview?.verified}
                      </P>
                    </View>
                    <View style={styles.statItem}>
                      <P style={styles.statLabel}>Unverified</P>
                      <P style={styles.statValue}>
                        {referralOverview?.unVerified}
                      </P>
                    </View>
                  </View>
                </View>

                {/* End New Referral Contest Section */}
                {/* <TouchableOpacity
                onPress={() => {
                  navigation.navigate("ReferralProgramListScreen");
                }}
              >
                <View style={styles.tref}>
                  <View style={styles.totRef}>
                    <SvgXml xml={svg.pplGreen} />
                    <View>
                      <P style={[styles.ct_Text, { fontSize: 12 }]}>
                        Total referral
                      </P>
                      <P>{totalRef}</P>
                    </View>
                  </View>
                  <SvgXml xml={svg.chevLeft} />
                </View>
              </TouchableOpacity> */}
                <View style={styles.countDownCont}>
                  <CountdownRing totalSeconds={countdownSeconds} />
                </View>
                <View style={{ marginTop: 16 }}>
                  <View style={{ alignItems: "center", width: "100%" }}>
                    <FlatList
                      data={notes}
                      ref={noteFlatListRef}
                      renderItem={({ item }) => (
                        <NoteComponent2
                          contStyle={{
                            backgroundColor: "#FFFFFF",
                            width: screenWidth * 0.9,
                            alignSelf: "center",
                          }}
                          component={
                            <P
                              style={{
                                fontSize: 10,
                                lineHeight: 16,
                                fontFamily: fonts.poppinsRegular,
                                color: colors.black,
                              }}
                            >
                              {item.map ? item.map((part, idx) => part) : item}
                            </P>
                          }
                        />
                      )}
                      horizontal
                      pagingEnabled
                      keyExtractor={(_, idx) => idx.toString()}
                      showsHorizontalScrollIndicator={false}
                      scrollEnabled={notes.length > 1}
                      onMomentumScrollEnd={(event) => {
                        const index = Math.round(
                          event.nativeEvent.contentOffset.x /
                            (screenWidth * 0.92)
                        );
                        setActiveNoteIndex(index);
                      }}
                      getItemLayout={(_, index) => ({
                        length: screenWidth * 0.92,
                        offset: screenWidth * 0.92 * index,
                        index,
                      })}
                      style={{ width: "100%" }}
                    />
                    {notes.length > 1 && (
                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "center",
                          marginTop: 8,
                        }}
                      >
                        {notes.map((_, idx) => (
                          <View
                            key={idx}
                            style={{
                              width: 8,
                              height: 8,
                              borderRadius: 4,
                              backgroundColor: colors.primary,
                              opacity: activeNoteIndex === idx ? 1 : 0.3,
                              marginHorizontal: 4,
                            }}
                          />
                        ))}
                      </View>
                    )}
                  </View>
                </View>
                <View style={styles.detailWrap2}>
                  <View style={styles.deatilsHead}>
                    <P style={{ color: colors.black, fontSize: 12 }}>
                      Leaderboard
                    </P>
                    <Link
                      style={{ fontSize: 12, textDecorationLine: "underline" }}
                      onPress={() => {
                        navigation.navigate("ViewMoreLeaderboard");
                      }}
                    >
                      View more
                    </Link>
                  </View>
                  {rankLoading ? (
                    <View
                      style={{
                        height: 150,
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <ActivityIndicator
                        color={colors.primary}
                        size={"large"}
                      />
                    </View>
                  ) : ranking.length === 0 ? (
                    <View style={{ width: "100%", alignItems: "center" }}>
                      <SvgXml
                        xml={svg.pplPrimary}
                        style={{ marginTop: 52, marginBottom: 16 }}
                      />
                      <P
                        style={{
                          fontFamily: fonts.poppinsMedium,
                          marginBottom: 4,
                        }}
                      >
                        No top referrals!
                      </P>
                      <P
                        style={{
                          color: "#A5A1A1",
                          fontFamily: fonts.poppinsRegular,
                          width: 190,
                          fontSize: 11,
                          lineHeight: 14,
                          textAlign: "center",
                        }}
                      >
                        No active top referals at the moment
                      </P>
                    </View>
                  ) : (
                    <>
                      {ranking.slice(0, 5).map((item, index) => (
                        <View
                          key={index}
                          style={{
                            width: "100%",
                            paddingTop: 13,
                            paddingBottom: 13,
                            paddingLeft: 16,
                            paddingRight: 16,
                            borderBottomWidth:
                              index === ranking.length - 1 ? 0 : 1,
                            borderColor: colors.secBackground,
                            flexDirection: "row",
                            justifyContent: "space-between",
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                            }}
                          >
                            <P style={{ marginRight: 12 }}>{index + 1}</P>
                            <View>
                              <Image
                                //   source={{
                                //     uri: ensureHttps(item?.referredUser?.picture),
                                //   }}
                                source={
                                  item?.user?.picture
                                    ? { uri: ensureHttps(item?.user?.picture) }
                                    : require("../../assets/user1.png")
                                }
                                style={{
                                  width: 40,
                                  height: 40,
                                  borderRadius: 100,
                                  objectFit: "cover",
                                }}
                              />
                              <SvgXml
                                xml={
                                  item?.rank == 1
                                    ? svg.GoldBadge
                                    : item?.rank == 2
                                    ? svg.silver
                                    : item?.rank == 3
                                    ? svg.Bronze3
                                    : item?.rank == 4
                                    ? svg.Bronze4
                                    : svg.Bronze5
                                }
                                style={{
                                  position: "absolute",
                                  bottom: -3,
                                  right: -3,
                                }}
                                width={20}
                                height={20}
                              />
                            </View>
                            <View style={{ marginLeft: 12 }}>
                              <P style={{ fontSize: 12 }}>
                                {item?.user?.firstName} {item?.user?.lastName}
                              </P>
                              <View
                                style={{
                                  flexDirection: "row",
                                  alignItems: "center",
                                  gap: 4,
                                }}
                              >
                                <SvgXml xml={svg.pplOutline} />
                                <P
                                  style={{
                                    fontSize: 12,
                                    color: colors.dGray,
                                    fontFamily: fonts.poppinsRegular,
                                  }}
                                >
                                  {item?.referredCount?.toLocaleString()}{" "}
                                  {item?.referredCount > 1
                                    ? "referrals"
                                    : "referral"}
                                </P>
                              </View>
                            </View>
                          </View>
                        </View>
                      ))}
                    </>
                  )}
                </View>
                <View style={styles.detailWrap2}>
                  <View style={styles.deatilsHead}>
                    <P style={{ color: colors.black, fontSize: 12 }}>
                      Referrals
                    </P>
                    {referrals.length > 2 ? (
                      <Link
                        style={{
                          fontSize: 12,
                          textDecorationLine: "underline",
                        }}
                        onPress={() => {
                          navigation.navigate("ReferralProgramListScreen", {
                            overView: referralOverview,
                            code: referralCode,
                          });
                        }}
                      >
                        View all
                      </Link>
                    ) : (
                      <></>
                    )}
                  </View>
                  {refLoading ? (
                    <View
                      style={{
                        height: 150,
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <ActivityIndicator
                        color={colors.primary}
                        size={"large"}
                      />
                    </View>
                  ) : referrals.length === 0 ? (
                    <View style={{ width: "100%", alignItems: "center" }}>
                      <SvgXml
                        xml={svg.pplPrimary}
                        style={{ marginTop: 52, marginBottom: 16 }}
                      />
                      <P
                        style={{
                          fontFamily: fonts.poppinsMedium,
                          marginBottom: 4,
                        }}
                      >
                        No top referrals!
                      </P>
                      <P
                        style={{
                          color: "#A5A1A1",
                          fontFamily: fonts.poppinsRegular,
                          width: 190,
                          fontSize: 11,
                          lineHeight: 14,
                          textAlign: "center",
                        }}
                      >
                        You have no referral yet, copy your referral code and
                        share link
                      </P>
                    </View>
                  ) : (
                    <>
                      {referrals.slice(0, 3).map((item, index) => (
                        <View
                          key={index}
                          style={{
                            width: "100%",
                            paddingTop: 13,
                            paddingBottom: 13,
                            paddingLeft: 16,
                            paddingRight: 16,
                            borderBottomWidth: index === 2 ? 0 : 1,
                            borderColor: colors.secBackground,
                            flexDirection: "row",
                            justifyContent: "space-between",
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                            }}
                          >
                            <View>
                              <Image
                                source={
                                  item?.referredUser?.picture
                                    ? {
                                        uri: ensureHttps(
                                          item?.referredUser?.picture
                                        ),
                                      }
                                    : require("../../assets/user1.png")
                                }
                                style={{
                                  width: 40,
                                  height: 40,
                                  borderRadius: 100,
                                  objectFit: "cover",
                                }}
                              />
                            </View>
                            <View style={{ marginLeft: 12 }}>
                              <P style={{ fontSize: 12 }}>
                                {item?.referredUser?.firstName
                                  ? `${item?.referredUser?.firstName} ${item?.referredUser?.lastName}`
                                  : "Guest"}
                              </P>
                              <View
                                style={{
                                  flexDirection: "row",
                                  alignItems: "center",
                                  gap: 4,
                                }}
                              >
                                <P
                                  style={{
                                    fontSize: 12,
                                    color: colors.dGray,
                                    fontFamily: fonts.poppinsRegular,
                                  }}
                                >
                                  {formatDate(item?.createdAt)}
                                </P>
                              </View>
                            </View>
                          </View>
                          <P
                            style={{
                              position: "absolute",
                              right: 16,
                              alignSelf: "center",
                              fontSize: 12,
                              color:
                                item?.referredUser?.verified === "true"
                                  ? colors.green
                                  : colors.red,
                            }}
                          >
                            {item?.referredUser?.verified === "true"
                              ? "Verified"
                              : "Unverified"}
                          </P>
                        </View>
                      ))}
                    </>
                  )}
                </View>
              </View>
            </ScrollView>
            <View
              style={[
                styles.bottomFloat,
                Platform.OS === "ios"
                  ? {
                      shadowColor: colors.gray,
                      shadowOffset: { width: 0, height: 1 },
                      shadowOpacity: 0.5,
                      shadowRadius: 4,
                    }
                  : { elevation: 20 },
              ]}
            >
              <View style={styles.shareComp}>
                <Button
                  style={{ height: 48 }}
                  btnText="Invite now"
                  onPress={() => {
                    setShowBottomSheet(true);
                  }}
                />
              </View>
            </View>
            {/* <ReferralShare code="tFh273" /> */}
          </Div>
        </View>
      </LinearGradient>

      <BottomSheet
        isVisible={showBottomSheet}
        showBackArrow={false}
        backspaceText="Important Notice"
        onClose={() => setShowBottomSheet(false)}
        modalContentStyle={{ height: !showStep ? "40%" : "65%" }}
        extraModalStyle={{ height: "62%" }}
        components={
          <ScrollView
            style={styles.bottomSheetContent}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 100 }}
          >
            <TouchableOpacity
              style={[
                styles.howItWorksHeader,
                { marginBottom: showStep ? 8 : 24 },
              ]}
              onPress={() => {
                setShowStep(!showStep);
              }}
            >
              <P style={styles.howItWorksTitle}>How it works?</P>
              <SvgXml xml={showStep ? svg.chevronUp : svg.chevronDown} />
            </TouchableOpacity>
            {showStep && (
              <View style={styles.stepsContainer}>
                <View style={styles.stepItem}>
                  <View style={styles.trackWrap}>
                    <View style={styles.stepNumberContainer}>
                      <P style={styles.stepNumber}>1</P>
                    </View>
                    <View style={styles.line} />
                  </View>
                  <View style={styles.stepTextContent}>
                    <P style={styles.stepTitle}>Referral code or link</P>
                    <P style={styles.stepDescription}>
                      Copy your unique referral link or code below
                    </P>
                  </View>
                </View>
                <View style={styles.stepItem}>
                  <View style={styles.trackWrap}>
                    <View style={styles.stepNumberContainer}>
                      <P style={styles.stepNumber}>2</P>
                    </View>
                    <View style={styles.line} />
                  </View>
                  <View style={styles.stepTextContent}>
                    <P style={styles.stepTitle}>Share</P>
                    <P style={styles.stepDescription}>
                      Share the link with friends and families
                    </P>
                  </View>
                </View>
                <View style={styles.stepItem}>
                  <View style={styles.trackWrap}>
                    <View style={styles.stepNumberContainer}>
                      <P style={styles.stepNumber}>3</P>
                    </View>
                  </View>
                  <View style={styles.stepTextContent}>
                    <P style={styles.stepTitle}>
                      Referral counts after verification
                    </P>
                    <P style={styles.stepDescription}>
                      Your referral is counted once they sign up and
                      successfully verify their account
                    </P>
                  </View>
                </View>
              </View>
            )}
            <View style={styles.shareComp1}>
              <View style={styles.textSection}>
                <P style={{ fontSize: 11, color: colors.dGray }}>
                  Your referral code
                </P>
                <P style={{ fontFamily: fonts.poppinsSemibold, fontSize: 13 }}>
                  {referralCode}
                </P>
              </View>
              <TouchableOpacity
                style={[
                  styles.buttonSection,
                  { backgroundColor: colors.secBackground },
                ]}
                onPress={() => {
                  copyToClipboard(referralCode);
                }}
              >
                <SvgXml xml={svg.copy} width={24} height={24} />
              </TouchableOpacity>
              <View style={styles.overLayBorder}></View>
            </View>
            <View style={[styles.shareComp1, { marginTop: 16 }]}>
              <View style={styles.textSection}>
                <P style={{ fontSize: 11, color: colors.dGray }}>
                  Your referral code
                </P>
                <P style={{ fontFamily: fonts.poppinsSemibold, fontSize: 13 }}>
                  {referralCode}
                </P>
              </View>
              <TouchableOpacity
                style={[styles.buttonSection]}
                onPress={onShare}
              >
                <SvgXml xml={svg.shareWhite} />
              </TouchableOpacity>
              <View style={styles.overLayBorder}></View>
            </View>
          </ScrollView>
        }
      />
    </>
  );
}

const styles = StyleSheet.create({
  body: {},
  gradient: {
    width,
    height,
    flex: 1,
  },
  bgImg: {
    width: 180,
    height: 180,
    position: "absolute",
    right: 0,
    top: 43,
    zIndex: 0,
    opacity: 0.2,
  },
  ItemBody: {
    width,
    height: "100%",
    // position: "absolute",
  },
  navText: {
    fontSize: 12,
    color: colors.white,
    textDecorationLine: "underline",
  },
  contentBody: {
    width: "90%",
    alignSelf: "center",
    marginTop: 8,
  },
  ct_Text: {
    fontSize: 20,
    lineHeight: 24,
    color: colors.white,
    fontFamily: fonts.poppinsSemibold,
    textAlign: "center",
  },
  ct_HText: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.white,
    textAlign: "center",
  },
  tref: {
    width: "100%",
    height: 71,
    backgroundColor: colors.white,
    marginTop: 24,
    borderRadius: 12,
    flexDirection: "row",
    padding: 16,
    justifyContent: "space-between",
    alignItems: "center",
  },
  totRef: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  countDownCont: {
    width: "100%",
    backgroundColor: colors.white,
    borderRadius: 12,
    // marginTop: 16,
  },
  detailWrap2: {
    // padding: 24,
    width: "100%",
    alignSelf: "center",
    minHeight: 246,
    backgroundColor: "white",
    borderRadius: 12,
    alignItems: "center",
    marginTop: 16,
  },
  deatilsHead: {
    width: "100%",
    height: 42,
    borderBottomWidth: 1,
    borderColor: colors.secBackground,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: "5%",
  },
  contestCard: {
    width: "100%",
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  contestHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  contestTitle: {
    fontSize: 16,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
    marginTop: 4,
  },
  contestSubtitle: {
    fontSize: 12,
    color: colors.dark500,
    fontFamily: fonts.poppinsRegular,
  },
  contestBagImage: {
    width: 100,
    height: 100,
    borderRadius: 12,
    objectFit: "cover",
  },
  dashedDivider: {
    width: "100%",
    height: 0,
    backgroundColor: colors.secBackground,
    borderStyle: "dashed",
    borderWidth: Platform.OS === "ios" ? 0.8 : 1,
    borderColor: colors.stroke,
    marginVertical: 16,
  },
  referralStatsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
  },
  statLabel: {
    fontSize: 12,
    color: colors.dark500,
    fontFamily: fonts.poppinsRegular,
  },
  statValue: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.poppinsMedium,
  },
  bottomFloat: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    paddingVertical: 20,
    paddingHorizontal: 20,
    alignItems: "center",
  },
  shareComp: {
    width: "80%",
    height: 48,
  },
  shareComp1: {
    width: "100%",
    height: 53,
    flexDirection: "row",
    borderRadius: 8,
    overflow: "hidden",
  },
  // BottomSheet Styles
  bottomSheetContent: {
    // paddingHorizontal: 20,
    // paddingBottom: 20,
  },
  howItWorksHeader: {
    flexDirection: "row",
    // justifyContent: "center",
    alignItems: "center",
    gap: 4,
    paddingVertical: 15,
    // marginBottom:  8,
  },
  howItWorksTitle: {
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    color: colors.black,
  },
  stepsContainer: {
    marginBottom: 30,
  },
  stepItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    // marginBottom: 15,
  },
  stepNumberContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#22C26E",
    justifyContent: "center",
    alignItems: "center",
  },
  stepNumber: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.white,
  },
  stepTextContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.dark500,
    marginBottom: 2,
  },
  stepDescription: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.black,
  },
  referralCodeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: colors.secBackground,
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
  },
  referralLabel: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.dGray,
  },
  referralValue: {
    fontSize: 16,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
    marginTop: 5,
  },
  copyButton: {
    padding: 10,
  },
  referralLinkContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingLeft: 15,
    paddingRight: 10,
    height: 70, // Fixed height for alignment
  },
  shareButton: {
    width: 60,
    height: "100%",
    backgroundColor: colors.primary,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  textSection: {
    width: "80%",
    height: "100%",
    backgroundColor: colors.secBackground,
    paddingLeft: 16,
    justifyContent: "center",
  },
  buttonSection: {
    width: "20%",
    height: "100%",
    backgroundColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
  },
  overLayBorder: {
    width: "100%",
    position: "absolute",
    height: 53,
    borderWidth: 1.2,
    borderRadius: 8,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    pointerEvents: "none",
  },
  line: {
    height: 28,
    width: 2,
    borderRadius: 2,
    backgroundColor: "#CDF4E3",
    marginTop: 4,
    marginBottom: 4,
  },
  trackWrap: {
    alignItems: "center",
    marginRight: 12,
  },
  ct_T: {
    textAlign: "center",
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    marginBottom: 8,
  },
});
