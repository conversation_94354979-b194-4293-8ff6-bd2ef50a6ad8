import React, { useRef, useState, useEffect, useContext } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  FlatList,
  Text,
} from "react-native";
import Div from "../components/Div";
import { colors } from "../config/colors";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import H4 from "../components/H4";
import Button from "../components/Button";
import Link from "../components/Link";
import i18n from "../../i18n";
import { LanguageContext } from "../context/LanguageContext";
import AsyncStorage from "@react-native-async-storage/async-storage";

const { width, height } = Dimensions.get("window");
const isTablet = Dimensions.get("window").width >= 700;
export default function OnboardingScreen({ navigation }) {
  // @ts-ignore
  const { language, changeLanguage } = useContext(LanguageContext);
  const [isTermAgreed, setIsTermAgreed] = useState(false);
  const carouselData = [
    {
      img: require("../assets/img1.png"),
      text1: i18n.t("onB.text11"),
      text2: i18n.t("onB.text21"),
    },
    {
      img: require("../assets/img2.png"),
      text1: i18n.t("onB.text21"),
      text2: i18n.t("onB.text22"),
    },
    {
      img: require("../assets/img3.png"),
      text1: i18n.t("onB.text31"),
      text2: i18n.t("onB.text32"),
    },
  ];

  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef(null);

  useEffect(() => {
    const interval = setInterval(() => {
      if (activeIndex === carouselData.length - 1) {
        flatListRef.current.scrollToIndex({ index: 0, animated: false });
        setActiveIndex(0);
      } else {
        setActiveIndex((prevIndex) => prevIndex + 1);
      }
    }, 3000); // Change image every 3 seconds

    return () => clearInterval(interval);
  }, [activeIndex]);

  useEffect(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToIndex({ index: activeIndex, animated: true });
    }
  }, [activeIndex]);

  const onViewableItemsChanged = useRef(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      setActiveIndex(viewableItems[0].index);
    }
  }).current;
  const viewabilityConfig = {
    itemVisiblePercentThreshold: 50,
  };

  // Define getItemLayout for FlatList to optimize scrollToIndex
  const getItemLayout = (_: any, index: number) => ({
    length: width,
    offset: width * index,
    index,
  });

  // Handle scroll failures
  const handleScrollToIndexFailed = (info: { index: number; highestMeasuredFrameIndex: number; averageItemLength: number }) => {
    const wait = new Promise(resolve => setTimeout(resolve, 500));
    wait.then(() => {
      if (flatListRef.current) {
        flatListRef.current.scrollToIndex({
          index: info.index,
          animated: true,
          viewPosition: 0.5
        });
      }
    });
  };
  const checkTerms = async () => {
    try {
      const res = await AsyncStorage.getItem("aggrr###");
      if (res !== null) {
        setIsTermAgreed(true);
      } else {
        setIsTermAgreed(false);
      }
    } catch (error) {}
  };

  useEffect(() => {
    AsyncStorage.setItem("justLoggedIn", "true")
      .then(() => console.log("Set justLoggedIn flag on login page"))
      .catch((err) => console.error("Error setting justLoggedIn flag:", err));
    checkTerms();
  }, []);

  const renderItem = ({ item }) => (
    <View style={styles.imageContainer}>
      <Image source={item.img} style={styles.image} />
      <H4 style={styles.text1}>{item.text1}</H4>
      <P style={styles.text2}>{item.text2}</P>
    </View>
  );

  return (
    <View style={styles.container}>
      <Div>
        <View style={{ minHeight: (60 * height) / 100 }}>
          <FlatList
            ref={flatListRef}
            data={carouselData}
            renderItem={renderItem}
            style={styles.flatList}
            horizontal
            pagingEnabled
            keyExtractor={(_, index) => index.toString()}
            showsHorizontalScrollIndicator={false}
            onViewableItemsChanged={onViewableItemsChanged}
            viewabilityConfig={viewabilityConfig}
            getItemLayout={getItemLayout}
            onScrollToIndexFailed={handleScrollToIndexFailed}
          />
        </View>
        <View style={styles.dotContainer}>
          {carouselData.map((_, index) => (
            <View
              key={index}
              style={[
                styles.dot,
                {
                  backgroundColor:
                    index === activeIndex
                      ? colors.primary
                      : colors.lowOpPrimary,
                },
              ]}
            />
          ))}
        </View>

        <View style={styles.bottomCont}>
          <Button
            btnText={i18n.t("onB.btnText")}
            btnTextStyle={{ fontSize: 12 }}
            onPress={() =>
              // !isTermAgreed
              //   ? navigation.navigate("BetaTest", {
              //       nextRoute: "SignupScreen",
              //     })
              //   :
              navigation.navigate("SignupScreen")
            }
          />
          <Link
            style={{ textAlign: "center", marginTop: 26, fontSize: 12 }}
            onPress={() => {
              // !isTermAgreed
              //   ? navigation.navigate("BetaTest", {
              //       nextRoute: "NewLoginScreen",
              //     })
              //   :
              navigation.navigate("NewLoginScreen");
            }}
          >
            {i18n.t("onB.linkText")}
          </Link>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  flatList: {
    height: "auto",
  },
  imageContainer: {
    width,
    minHeight: (60 * height) / 100,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 55,
  },
  image: {
    width: (90 * width) / 100,
    height: (50 * height) / 100,
    objectFit: "contain",
  },
  text1: { textAlign: "center" },
  text2: {
    fontSize: 12,
    color: colors.gray,
    textAlign: "center",
    marginTop: 4,
    width: "90%",
    lineHeight: 22.4,
    fontFamily: fonts.poppinsRegular,
  },
  dotContainer: {
    flexDirection: "row",
    alignSelf: "center",
    marginTop: 16,
  },
  dot: {
    width: 16,
    height: 4,
    borderRadius: 99,
    backgroundColor: colors.lowOpPrimary,
    marginTop: 0,
    marginBottom: 0,
    margin: 5,
  },
  bottomCont: {
    width: (90 * width) / 100,
    alignSelf: "center",
    position: "absolute",
    bottom: 42,
    justifyContent: "center",
  },
});
