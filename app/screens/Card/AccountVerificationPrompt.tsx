import React, { useState, useEffect, useCallback } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Linking,
  Platform,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import * as Clipboard from "expo-clipboard";
import Link from "../../components/Link";
import NoteComponent2 from "../../components/NoteComponent2";
import { CheckSession, GetUserDetails } from "../../RequestHandlers/User";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../components/ActivityIndicator";
import { useToast } from "../../context/ToastContext";
import Dash from "../../components/Dash";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import AsyncStorage from "@react-native-async-storage/async-storage";

const baseHeight = 800;
const { width, height } = Dimensions.get("window");
export default function AccountVerificationPromt({ navigation }) {
  const [isPInfoCreated, setPInfoCreated] = useState(false);
  const [isUserNameCreated, setIsUserNameCreated] = useState(false);
  const [isTPinCreated, setIsTPinCreated] = useState(false);
  const [isIdVerified, setIsIdVerified] = useState(false);
  const [loader, setLoader] = useState(false);
  const { handleToast } = useToast();
  // const [isAccVerified, setAccVerified] = useState(false);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  const getUserDetails = async () => {
    setLoader(true);
    try {
      const userDetails = await GetUserDetails();
      if (userDetails) {
        setLoader(false);
      }
      if (userDetails?.homeCountry) {
        setPInfoCreated(true);
      } else {
        setPInfoCreated(false);
      }
      if (userDetails?.username) {
        setIsUserNameCreated(true);
      } else {
        setIsUserNameCreated(false);
      }
      if (userDetails?.hasPin) {
        setIsTPinCreated(true);
      } else {
        setIsTPinCreated(false);
      }
      if (userDetails?.verified === "true") {
        setIsIdVerified(true);
      } else {
        setIsIdVerified(false);
      }
    } catch (error) {
      handleToast("Network error", "error");
    }
  };
  useFocusEffect(
    useCallback(() => {
      getUserDetails();
    }, [])
  );
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Account verification"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentCard}>
            <View style={styles.section1Wrap}>
              <H4 style={styles.amt}>Verify your account</H4>

              <H4
                // @ts-ignore
                style={[
                  styles.amt,
                  {
                    fontSize: 12,
                    lineHeight: (19.2 / baseHeight) * height,
                    color: colors.gray,
                    textAlign: "center",
                    fontFamily: fonts.poppinsRegular,
                  },
                ]}
              >
                Complete your account verification to unlock exclusive offers
                from the money app
              </H4>
              {Platform.OS === "ios" ? (
                <Dash />
              ) : (
                <View style={styles.dash}></View>
              )}
              <View style={styles.section2Wrap}>
                <NoteComponent2 text="This process ensures that your account remains safe and secure." />
              </View>
              <View style={styles.section3Wrap}>
                <View style={styles.progressDesCont}>
                  <View style={{ alignItems: "center", marginRight: 12 }}>
                    <SvgXml
                      xml={
                        isPInfoCreated
                          ? svg.circleSuccess
                          : svg.circleInprogress
                      }
                    />
                    <View
                      style={[
                        styles.bar2,
                        {
                          backgroundColor: isPInfoCreated
                            ? colors.green
                            : colors.lowOpPrimary2,
                        },
                      ]}
                    ></View>
                  </View>
                  <View>
                    <P style={styles.progTextHead}>Personal information</P>
                    <P style={styles.progTextBody}>
                      Fill your contact information
                    </P>
                  </View>
                </View>

                <View style={styles.progressDesCont}>
                  <View style={{ alignItems: "center", marginRight: 12 }}>
                    <SvgXml
                      xml={
                        isUserNameCreated
                          ? svg.circleSuccess
                          : isPInfoCreated
                          ? svg.circleInprogress
                          : svg.circleNull
                      }
                    />
                    <View
                      style={[
                        styles.bar2,
                        {
                          backgroundColor:
                            isUserNameCreated === true
                              ? colors.green
                              : colors.lowOpPrimary2,
                        },
                      ]}
                    ></View>
                  </View>
                  <View>
                    <P style={styles.progTextHead}>Username</P>
                    <P style={styles.progTextBody}>
                      Create your money app username
                    </P>
                  </View>
                </View>
                <View style={styles.progressDesCont}>
                  <View style={{ alignItems: "center", marginRight: 12 }}>
                    <SvgXml
                      xml={
                        isTPinCreated
                          ? svg.circleSuccess
                          : isUserNameCreated
                          ? svg.circleInprogress
                          : svg.circleNull
                      }
                    />
                    <View
                      style={[
                        styles.bar2,
                        {
                          backgroundColor:
                            isTPinCreated === true
                              ? colors.green
                              : colors.lowOpPrimary2,
                        },
                      ]}
                    ></View>
                  </View>
                  <View>
                    <P style={styles.progTextHead}>Transaction PIN</P>
                    <P style={styles.progTextBody}>
                      Create your transaction PIN
                    </P>
                  </View>
                </View>

                <View style={styles.progressDesCont}>
                  <View style={{ alignItems: "center", marginRight: 12 }}>
                    <SvgXml
                      xml={
                        isIdVerified
                          ? svg.circleSuccess
                          : isTPinCreated
                          ? svg.circleInprogress
                          : svg.circleNull
                      }
                    />
                  </View>
                  <View>
                    <P style={styles.progTextHead}>Identity verification</P>
                    <P style={styles.progTextBody}>
                      Complete your identity verification
                    </P>
                  </View>
                </View>
              </View>
            </View>
          </View>

          <P
            style={{
              textAlign: "center",
              marginTop: 16,
              fontSize: 12,
              lineHeight: 18,
              color: colors.gray,
              fontFamily: fonts.poppinsRegular,
            }}
          >
            Security and comply with regulatory requirements
          </P>
          <View style={styles.btnCont}>
            <Button
              btnText={isIdVerified ? "Done" : "Verify account"}
              onPress={() => {
                if (!isPInfoCreated) {
                  navigation.navigate("AccountVerification1");
                } else if (!isUserNameCreated) {
                  navigation.navigate("AccountVerification2");
                } else if (!isTPinCreated) {
                  navigation.navigate("AccountVerification3");
                } else if (!isIdVerified) {
                  navigation.navigate("AccountVerification4");
                } else {
                  navigation.navigate("BottomTabNavigator");
                }
              }}
            />
          </View>
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    // marginTop: 24,
    marginTop: (2.7 * height) / 100,
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 16,
    paddingRight: 16,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    marginBottom: 4,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  copyBtn: {
    paddingTop: 4,
    paddingBottom: 4,
    padding: 13,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  addMoney: {
    fontSize: 12,
    lineHeight: 19.2,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  amt: {
    fontSize: 16,
    // fontSize: 32,
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  amtCur: {
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  timer: {
    paddingTop: 4,
    paddingBottom: 4,
    paddingLeft: 16,
    paddingRight: 16,
    borderRadius: 99,
    marginTop: 16,
  },
  statusText: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsRegular,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: (2.7 * height) / 100,
  },
  section3Wrap: {
    width: "100%",
    marginTop: (2.7 * height) / 100,
    borderColor: colors.stroke,
    paddingLeft: 16,
  },
  barCont: {
    // backgroundColor: "red",
    alignItems: "center",
  },
  bar1: {
    height: 46,
    width: 2,
    borderRadius: 2,
    backgroundColor: colors.green,
    marginTop: 4,
    marginBottom: 4,
  },
  bar2: {
    height: 28,
    width: 2,
    borderRadius: 2,
    marginTop: 4,
    marginBottom: 4,
  },
  progressDesCont: {
    flexDirection: "row",
  },
  progTextHead: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  progTextBody: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: (5 * height) / 100,
    // marginTop: 42,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
  dash: {
    width: "100%",
    height: 1,
    borderWidth: 0.5,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    marginTop: (2.7 * height) / 100,
  },
});
