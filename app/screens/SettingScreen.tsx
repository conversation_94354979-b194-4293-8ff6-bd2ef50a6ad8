import React, {
  useState,
  useEffect,
  useRef,
  useContext,
  useCallback,
} from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
  Image,
  Button,
  Linking,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import UserHeader from "../components/UserHeader";
import Div from "../components/Div";
import MicroBtn from "../components/MicroBtn";
import { svg } from "../config/Svg";
import { SvgXml } from "react-native-svg";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";
import Content1 from "../components/Content1";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import Loader from "../components/ActivityIndicator";
import { useFocusEffect } from "@react-navigation/native";
import { GetUserDetails } from "../RequestHandlers/User";
import { GetUserWallet } from "../RequestHandlers/Wallet";

import BarCodeScanner from "../components/BarCodeScanner";
import { GetNotifications } from "../RequestHandlers/notification";
import * as Clipboard from "expo-clipboard";
import { GetSfxPoint } from "../RequestHandlers/User";
import { useToast } from "../context/ToastContext";
import InAppReview from "react-native-in-app-review";
import UserDetailsSkeleton from "../components/UserDetailsSkeleton";
import { GetReferalCountdown } from "../RequestHandlers/Referral";
import { formatToTwoDecimals } from "../Utils/numberFormat";
import { withApiErrorToast } from "../Utils/withApiErrorToast";

// import BillPaymentScreen from "./BillPaymentScreen";

const { width, height } = Dimensions.get("window");

export default function SettingScreen({ navigation }) {
  const { handleToast } = useToast();
  const [amount, setAmount] = useState(0);
  const [amc, setAmc] = useState("USD");
  const [isKycDone, setIsKycDone] = useState(false);
  const [showAddMoney, setShowAddMoney] = useState(false);
  const [activePaymentType, setActivePaymentType] = useState(null);
  const [showQrCode, setSHowQrCode] = useState(false);
  const [sfxPoint, setSfxPoint] = useState(0.0);
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const [securityLevel, setSecurityLevel] = useState("Medium");
  const handleScan = (index) => {
    if (index.length < 20) {
      navigation.navigate("AccountDetailsEntry", { data: index });
    } else if (index.length === 42) {
      navigation.navigate("P2pScreen", { data: index });
    } else if (index.length === 34) {
      navigation.navigate("P2pScreen", { data: index });
    } else {
      handleToast("invalid wallet address", "error");
    }
  };
  const [activeAccIndex, setActiveAccIndex] = useState(0);
  const activeAccIndexRef = useRef<number | null>(null);
  const activePaymentTypeRef = useRef<number | null>(null);
  const [cardRegisterd, setCardRegisterd] = useState(false);
  const [accStatus, setAccStatus] = useState(" ");
  const [isNewNoti, setIsNewNoti] = useState(false);
  const [loader, setLoader] = useState(false);
  const [uDetails, setUDetails] = useState<any>([]);
  const [isRefCopied, setIsRefCopied] = useState(false);
  const [isLoadingDetails, setIsLoadingDetails] = useState(true);
  const [countdownSeconds, setCountdownSeconds] = useState(0);
  const [isReferalProgram, setIsReferalProgram] = useState(true);

  const accDetails = `${uDetails.username}`;
  const copyAccNum = async () => {
    const copiedText = await Clipboard.setStringAsync(accDetails);

    if (copiedText === true) {
      setIsRefCopied(true);
      setTimeout(() => {
        setIsRefCopied(false);
      }, 4000);
    }
  };
  useEffect(() => {
    activeAccIndexRef.current = activeAccIndex;
  }, [activeAccIndex]);

  useEffect(() => {
    activePaymentTypeRef.current = activePaymentType;
  }, [activePaymentType]);
  const [hideBal, setHideBal] = useState(false);

  const clearLogin = async () => {
    setLoader(true);
    await GoogleSignin.signOut();
    setTimeout(() => {
      setLoader(false);
      AsyncStorage.removeItem("login2fa")
        .then(() => {})
        .catch((error) => {});
      AsyncStorage.removeItem("cookies")
        .then(() => {
          // @ts-ignore
          setStoredCredentails(null);
          setLoader(false);
        })
        .catch((error) => {});
    }, 3000);
  };
  const userDetails = async () => {
    try {
      const userDetails = await withApiErrorToast(
        GetUserDetails(),
        handleToast
      );
      setUDetails(userDetails);
      if (userDetails.verified === false) {
        setAccStatus("Bronze");
      } else {
        if (userDetails?.tier?.level === 1) {
          setAccStatus("Silver");
        } else {
          setAccStatus("Gold");
        }
      }
      // if (!userDetails.homeCountry) {
      //   navigation.navigate("AccountVerificationPromt");
      // } else if (!userDetails.username) {
      //   navigation.navigate("AccountVerificationPromt");

      // } else {
      // }
      if (userDetails?._2faEnabled === true) {
        setSecurityLevel("Strong");
      } else {
        setSecurityLevel("Medium");
      }
    } catch (error) {
    } finally {
      setIsLoadingDetails(false);
    }
  };

  const getUserWallet = async () => {
    try {
      const userWallet = await GetUserWallet();
      setAmount(userWallet?.totalInUsd);
    } catch (error) {}
  };

  const getAllNoti = async () => {
    try {
      const res = await GetNotifications(1, 20, "all");
      // (res.items[13].transaction);
      let hasUnreadPayment = false;
      res.items.forEach((notification) => {
        if (notification.status === "unread") {
          hasUnreadPayment = true;
        }
      });
      setIsNewNoti(hasUnreadPayment);
    } catch (error) {}
  };
  const getSfxPoint = async () => {
    try {
      const res = await GetSfxPoint();
      if (res.totalpoints) {
        setSfxPoint(res.totalpoints);
      }
    } catch (error) {}
  };
  const getReferralCountdown = async () => {
    try {
      const res = await withApiErrorToast(GetReferalCountdown(), handleToast);
      if (res.totalSeconds) {
        setCountdownSeconds(res.totalSeconds);
      }
    } catch (error) {}
  };
  useFocusEffect(
    useCallback(() => {
      userDetails();
      getUserWallet();
      getAllNoti();
      getSfxPoint();
    }, [])
  );

  useEffect(() => {
    getReferralCountdown();
  }, []);

  const requestReview = () => {
    if (InAppReview.isAvailable()) {
      InAppReview.RequestInAppReview()
        .then((hasFlowFinishedSuccessfully) => {
          // success or fail flow
          console.log(
            "In-App Review flow finished",
            hasFlowFinishedSuccessfully
          );
          if (hasFlowFinishedSuccessfully) {
            // handleToast("Thanks for reviewing our app", "success");
            // do something for ios
            // do something for android
          }
        })
        .catch((error) => {
          // error if any
          console.log(error);
        });
    }
  };
  return (
    <>
      <LinearGradient
        // colors={["rgba(139, 82, 255, 1)", "rgba(247, 244, 255, 1)"]}
        colors={[colors.lowOpPrimary2, colors.lowOpPrimary2]}
        locations={[0.2, 0.3]}
        style={styles.gradient}
      >
        <Div>
          <UserHeader
            // showName="Unverified"
            text1={
              isLoadingDetails ? (
                <UserDetailsSkeleton />
              ) : // @ts-ignore
              uDetails.firstName ? (
                // @ts-ignore
                `${uDetails.firstName} ${
                  uDetails?.middleName ? uDetails?.middleName : ""
                } ${
                  // @ts-ignore
                  uDetails.lastName ? uDetails.lastName : ""
                }`
              ) : (
                "..."
              )
            }
            showStatus={false}
            text1Style={{
              fontSize: 12,
              color: "#000",
              lineHeight: 18,
              marginBottom: 0,
            }}
            style={{
              color: "#000",
            }}
            icon1={svg.barCodeW2}
            brCodePressed={() => setSHowQrCode(true)}
            icon2={isNewNoti ? svg.activeNotification : svg.notiw2}
            onPress={() => {
              navigation.navigate("NotificationScreen");
            }}
            bottomComponent={
              isLoadingDetails ? (
                <>
                  <UserDetailsSkeleton />
                </>
              ) : (
                <View style={{ flexDirection: "row", marginTop: 4 }}>
                  {accStatus === "Silver" ? (
                    <Image
                      source={require("../assets/pBadge1.png")}
                      style={{ width: 24, height: 24, marginRight: 4 }}
                    />
                  ) : accStatus === "Gold" ? (
                    <Image
                      source={require("../assets/pBadge3.png")}
                      style={{ width: 24, height: 24, marginRight: 4 }}
                    />
                  ) : (
                    <>
                      <Image
                        source={require("../assets/pBadge2.png")}
                        style={{ width: 24, height: 24, marginRight: 4 }}
                      />
                    </>
                  )}
                  {isLoadingDetails ? (
                    <></>
                  ) : (
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 8,
                      }}
                    >
                      <P
                        style={{
                          fontSize: 12,
                          color: colors.gray,
                          fontFamily: fonts.poppinsMedium,
                        }}
                      >
                        {isRefCopied
                          ? "Username Copied"
                          : `@${
                              uDetails?.username ? uDetails?.username : "..."
                            }`}
                      </P>
                      <TouchableOpacity
                        onPress={() => {
                          copyAccNum();
                        }}
                      >
                        <View
                          style={{
                            width: 20,
                            height: 20,
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <SvgXml
                            xml={
                              isRefCopied ? svg.circleSuccess : svg.copy_gray
                            }
                          />
                        </View>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              )
            }
          />
          <ScrollView>
            {/* Account Balance */}

            <View style={[styles.card]}>
              <View style={styles.accountBalance}>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                    // backgroundColor: "red",
                    width: "100%",
                  }}
                >
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <P style={{ fontSize: 12 }}>Available asset balance</P>
                    <TouchableOpacity
                      style={{
                        marginLeft: 8,
                        width: 20,
                        height: 20,
                        justifyContent: "center",
                      }}
                      onPress={() => setHideBal((prevState) => !prevState)} // Toggle state
                    >
                      <SvgXml
                        xml={hideBal === true ? svg.eyeClose : svg.eyeOpen}
                      />
                      {/* Switch icon */}
                    </TouchableOpacity>
                  </View>
                  <P style={{ fontSize: 12 }}>Rewards</P>
                </View>
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    width: "100%",
                    alignItems: "center",
                    // marginTop: "2%",
                    //   backgroundColor:"red"
                  }}
                >
                  {/* @ts-ignore */}
                  <TouchableOpacity
                  //  onPress={() => toggleModal()}
                  >
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <P style={{ fontSize: 24, lineHeight: 36 }}>
                        {hideBal
                          ? "******"
                          : `$${formatToTwoDecimals(Number(amount))}`}{" "}
                        <Text
                          style={{
                            fontSize: 16,
                            fontFamily: fonts.poppinsMedium,
                          }}
                        >
                          {hideBal ? "***" : amc}
                        </Text>
                      </P>
                    </View>
                  </TouchableOpacity>
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <P
                      style={{
                        fontSize: 16,
                        fontFamily: fonts.poppinsMedium,
                      }}
                    >
                      {hideBal ? "*****" : sfxPoint}{" "}
                    </P>
                    <P
                      style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}
                    >
                      SFxp
                    </P>
                  </View>
                </View>
              </View>
            </View>
            <View style={styles.contentBody}>
              <View style={styles.detailWrap}>
                <Content1
                  svg1={svg.profile}
                  header="Profile"
                  onPress={() => navigation.navigate("ProfileIndex")}
                />
                <Content1
                  svg1={svg.securityLock}
                  header="Security"
                  onPress={() => navigation.navigate("SecurityIndex")}
                  rightComponent1={
                    <View
                      style={{
                        paddingHorizontal: 8,
                        paddingVertical: 2,
                        borderRadius: 100,
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 4,
                        backgroundColor:
                          securityLevel === "Medium"
                            ? colors.lowOpOrange
                            : colors.lowOpSuccess,
                      }}
                    >
                      <SvgXml
                        xml={
                          securityLevel === "Medium"
                            ? svg.signalMedium
                            : svg.signalFull
                        }
                      />
                      <P
                        style={{
                          fontSize: 10,
                          color:
                            securityLevel === "Medium"
                              ? colors.orange
                              : colors.green,
                        }}
                      >
                        {securityLevel}
                      </P>
                    </View>
                  }
                />

                <Content1
                  svg1={svg.notificationB}
                  header="Notification"
                  onPress={() => navigation.navigate("NotificationIndex")}
                />
                <Content1
                  svg1={svg.refer}
                  header="Refer & earn"
                  onPress={() => {
                    if (isReferalProgram) {
                      navigation.navigate("ReferralContestScreen", {
                        countdownSeconds,
                      });
                    } else {
                      navigation.navigate("ReferralProgramScreen");
                      // navigation.navigate("ReferralScreen")
                    }
                  }}
                />
                <Content1
                  svg1={svg.customer_Support}
                  header="Help & support"
                  onPress={() => {
                    const username = uDetails?.username || "Not provided";
                    const email = uDetails?.email || "Not provided";
                    const message = `Hi, Support, I have an issue that requires resolving.\nMy Username is ${username} and My email is ${email}`;
                    const encodedMessage = encodeURIComponent(message);
                    Linking.openURL(
                      `https://wa.me/905338563416?text=${encodedMessage}`
                    );
                  }}
                />
                <Content1
                  svg1={svg.scam}
                  header="Report scam"
                  onPress={() => {
                    const username = uDetails?.username || "Not provided";
                    const email = uDetails?.email || "Not provided";
                    const message = `Hi, Support, I have an issue that requires resolving.\nMy Username is ${username} and My email is ${email}`;
                    const encodedMessage = encodeURIComponent(message);
                    Linking.openURL(
                      `https://wa.me/905338563416?text=${encodedMessage}`
                    );
                  }}
                />
                <Content1
                  svg1={svg.rateus}
                  header="Rate us"
                  onPress={() => {
                    requestReview();
                  }}
                />
                <Content1
                  svg1={svg.aboutus}
                  header="About us"
                  bottomBorder={false}
                  onPress={() => navigation.navigate("AboutUs")}
                />
              </View>
              <TouchableOpacity
                style={styles.sat}
                onPress={() => {
                  clearLogin();
                }}
              >
                <P style={{ fontSize: 12, color: "gray" }}>Sign out</P>
              </TouchableOpacity>
              <View style={styles.sat2}>
                <P style={{ fontSize: 12, color: "gray" }}>Version:2.0</P>
              </View>
            </View>
          </ScrollView>
        </Div>
      </LinearGradient>
      <BarCodeScanner
        visible={showQrCode}
        onScan={handleScan}
        onClose={() => setSHowQrCode(false)}
      />
      <Loader visible={loader} />
    </>
  );
}

const styles = StyleSheet.create({
  gradient: {
    width,
    height,
    flex: 1,
  },
  card: {
    // backgroundColor: "red",
    borderRadius: 12,
    padding: 16,
    width: "90%",
    alignSelf: "center",
    // marginBottom: 16,
    // elevation: 4,
  },

  accountBalance: {
    alignItems: "flex-start",
    // backgroundColor:"red",
  },

  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    // paddingTop: 24,
    paddingBottom: 24,
    // marginTop: -16,
  },
  sat: {
    width: "90%",
    alignSelf: "center",
    height: 48,
    backgroundColor: "white",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 32,
  },
  sat2: {
    width: "90%",
    alignSelf: "center",
    height: 48,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 16,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    // height: 570,
    backgroundColor: "white",
    borderRadius: 12,
    alignItems: "center",
  },
});
