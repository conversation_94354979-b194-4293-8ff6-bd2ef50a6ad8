import React, { useState, useRef, useEffect } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import NoteComponent from "../../components/NoteComponent";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import CountrySelect from "../../components/CountrySelect";
import Button from "../../components/Button";
import ListItemSelect from "../../components/ListItemSelect";
import NoteComponent2 from "../../components/NoteComponent2";
import { NotMMCountires } from "../../components/NotMMCountries";
import Loader from "../../components/ActivityIndicator";
import { GetChannels, GetMinMAx, GetNetwork } from "../../RequestHandlers/Wallet";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import { useToast } from "../../context/ToastContext";
const { width, height } = Dimensions.get("window");
const baseWidth = 360;
export default function AddMoneyMobileMoneyAccountSelect({ navigation }) {
  const [paymentType, setPaymentType] = useState("USD coin");
  const [loading, setLoading] = useState(false);
  const [flag, setFlag] = useState(require("../../assets/kenya.png"));
  const [country, setCountry] = useState("");
  const [note, setNote] = useState("");
  const [showCountries, setShowCountries] = useState(false);
  const activeFlagRef = useRef<any | null>(null);
  const countryRef = useRef<String | null>(null);
  const yellowCardRef = useRef<String | null>(null);
  const currencyCodeRef = useRef<String | null>(null);
  const symbolRef = useRef<String | null>(null);
  const mobileCodeRef = useRef<String | null>(null);
  const alphaCode2Ref = useRef<String | null>(null);
  const [aplhaCode2, setAlphaCode2] = useState("");
  const [mobileCode, setMobileCode] = useState("");
  const [countryCode2, setCountryCode2] = useState("");
  const [moneyIcon, setMoneyIcon] = useState(require("../../assets/momo.png"));
  const [show2, setShow2] = useState(false);
  const [isproviderSelected, setIsProviderSelected] = useState(null);
  const [yellowCard, setYellowCard] = useState("");
  const [currencyCode, setCurrencyCode] = useState("");
  const [channelID, setChannelID] = useState("");
  const [networkID, setNetworkID] = useState("");
  const [symbol, setSymbol] = useState("");
  const [serviceProvider, setServiceProvider] = useState([]);
  const [phone, setPhone] = useState("");
  const [error, setError] = useState(false);
  const [fullName, setFullName] = useState("");
  const [nameError, setNameError] = useState(false);
  const [max, setMax] = useState(0);
  const [min, setMin] = useState(0);
  const [showChannelError, setShowChannelError] = useState(false);
  const [gateWayError, setGateWayError] = useState(false);
  const [providerError, setProviderError] = useState(false);
  const { handleToast} = useToast()
  const paymentTypes = [
    // { name: "USD(Tether)", rate: "1 USDT ~ 1 USD", icon: svg.tather },
    { name: "USD coin", rate: "1 USDC ~ 1 USD", icon: svg.usdCoin },
  ];
  const handleActiveCountry = (newActiveType: string | null) => {
    setCountry(newActiveType);
  };
  const handleActiveMobileCode = (newActiveType: string | null) => {
    setMobileCode(newActiveType);
  };
  const handleActiveFlag = (newActiveType: any | null) => {
    if (newActiveType) {
      setFlag(newActiveType);
    }
  };
  const handleActiveYellowCard = (newActiveType: string | null) => {
    setYellowCard(newActiveType);
  };
  const handleActiveCurrencyCode = (newActiveType: string | null) => {
    setCurrencyCode(newActiveType);
  };
  const handleActiveSymbol = (newActiveType: string | null) => {
    setSymbol(newActiveType);
  };
  const handleActiveAplhaCode2 = (newActiveType: string | null) => {
    setAlphaCode2(newActiveType);
  };
  useEffect(() => {
    alphaCode2Ref.current = aplhaCode2;
  }, [aplhaCode2]);
  useEffect(() => {
    countryRef.current = country;
  }, [country]);
  useEffect(() => {
    activeFlagRef.current = flag;
  }, [flag]);
  useEffect(() => {
    yellowCardRef.current = yellowCard;
  }, [yellowCard]);
  useEffect(() => {
    currencyCodeRef.current = currencyCode;
  }, [currencyCode]);
  useEffect(() => {
    symbolRef.current = symbol;
  }, [symbol]);
  useEffect(() => {
    mobileCodeRef.current = mobileCode;
  }, [mobileCode]);
  useEffect(() => {
    if (!country) {
      setFlag(require("../../assets/kenya.png"));
    }
  }, [country]);

  // Debug log to understand button state
  // useEffect(() => {
  //   // console.log("Button state:", {
  //   //   country,
  //   //   countryCode2,
  //   //   serviceProviderLength: serviceProvider.length,
  //   //   providerError,
  //   //   isDisabled: country === "" ||
  //   //              (serviceProvider.length > 0 && countryCode2 === "") ||
  //   //              providerError
  //   // });
  // }, [country, countryCode2, serviceProvider, providerError]);
  // Define network and channel types
  interface NetworkItem {
    id: string;
    name: string;
    channelIds: string[];
    [key: string]: any;
  }

  interface ChannelItem {
    id: string;
    rampType: string;
    channelType: string;
    max: number;
    min: number;
    [key: string]: any;
  }

  const getNetwork = async (code: string): Promise<NetworkItem[]> => {
    try {
      const networks = await GetNetwork(code);
      if (networks.error) {
        setGateWayError(true);
      } else {
        setGateWayError(false);
      }
      return networks;
    } catch (error) {
      console.error("Error fetching networks:", error);
      return [];
    }
  };

  const getChannels = async (code: string): Promise<ChannelItem[]> => {
    setLoading(true);
    try {
      const channels = await withApiErrorToast(GetChannels(code), handleToast);
      if (channels.error) {
        setShowChannelError(true);
        setGateWayError(true);
      } else {
        setShowChannelError(false);
        setGateWayError(false);
      }
      const result = channels.filter(
        (item: ChannelItem) => item.rampType === "deposit" && item.channelType === "momo"
      );
      if (result.length > 0) {
        setChannelID(result[0].id);
        result.forEach((item: ChannelItem) => {
          // setMax(item?.max);
          // setMin(item?.min);
        });
      }
      setLoading(false);
      return result;
    } catch (error) {
      console.error("Error fetching channels:", error);
      setLoading(false);
      return [];
    }finally{
      setLoading(false)
    }
  };

    const getMinMax = async (code)=>{
      try {
        const res = await GetMinMAx(code, "momo", "DEPOSIT")
        if (res.maximumAmountInLocal) {
          setMin(res.minimumAmountInLocal)
          setMax(res.maximumAmountInLocal)
        }
      } catch (error) {
      }
    }

  useEffect(() => {
    const fetchData = async () => {
      if (yellowCard) {
        getMinMax(yellowCard)
        const networks = await getNetwork(yellowCard);
        const channels = await getChannels(yellowCard);
        const ntw = networks
          .map((n: any) => ({
            ...n,
            matchingChannelCount: n.channelIds.filter(
              (id: string) => channels.some((channel: ChannelItem) => channel.id === id)
            ).length,
          }))
          .filter((n: any) => n.matchingChannelCount > 0)
          .sort(
            (a: any, b: any) => b.matchingChannelCount - a.matchingChannelCount
          );

        // Reset provider error state
     
        setProviderError(ntw.length === 0);
        setServiceProvider(ntw);
        if (ntw.length === 1) {
          setCountryCode2(ntw[0].name);
          setNetworkID(ntw[0].id);
        } else {
          setCountryCode2("");
          setNetworkID("");
        }
      }
    };
    fetchData();
  }, [yellowCard]);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Mobile money" navigation={navigation} />
        <ScrollView
          contentContainerStyle={{ paddingBottom: "20%" }}
          automaticallyAdjustKeyboardInsets={true}
        >
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              {/* <P
                style={{
                  fontSize: 12,
                  fontFamily: fonts.poppinsRegular,
                  marginBottom: 6,
                }}
              >
                Account
              </P> */}
              <View style={{ marginBottom: 16 }}>
                <NoteComponent2
                  text={`Please note that you will receive USD in your ${paymentType} account.`}
                />
              </View>
              {/* {paymentTypes.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => setPaymentType(item.name)}
                >
                  <View
                    style={[
                      styles.pyItem,
                      {
                        borderColor:
                          paymentType === item.name
                            ? colors.primary
                            : colors.stroke,
                        backgroundColor:
                          paymentType === item.name ? "#F7F4FF" : "transparent",
                      },
                    ]}
                  >
                    <SvgXml xml={item.icon} />
                    <View style={{ marginLeft: 8 }}>
                      <P style={styles.pyName}>{item.name}</P>
                      <P style={styles.rate}>{item.rate}</P>
                    </View>
                    {paymentType === item.name && (
                      <SvgXml
                        xml={svg.ppTick}
                        style={{ position: "absolute", right: 16 }}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              ))} */}
              <TouchableOpacity
                onPress={() => {
                  setShowCountries(true);
                }}
              >
                <View>
                  <Input
                    value={country}
                    label="Country"
                    placeholder="Kenya"
                    inputStyle={{ width: "65%", color: "#161817" }}
                    contStyle={{ marginBottom: 16 }}
                    editable={false}
                    leftIcon={
                      <Image
                        source={flag}
                        style={{
                          width: 24,
                          height: 24,
                          marginLeft: 14,
                          objectFit: "cover",
                          borderRadius: 100,
                        }}
                      />
                      //   <View>
                      //   </View>
                    }
                    rightIcon={
                      <View
                        style={{
                          //   backgroundColor: "red",
                          width: "15%",
                          height: "100%",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <SvgXml xml={svg.dropDown} />
                      </View>
                    }
                  />
                </View>
              </TouchableOpacity>
              {country != "" && (
                <>
                  <Input
                    label={`Sender Full name`}
                    placeholder="John Doe"
                    inputStyle={{ width: "80%" }}
                    // contStyle={{ marginBottom: 16 }}
                    value={fullName}
                    onChangeText={(e) => setFullName(e)}
                    error={nameError}
                  />

                  {nameError && (
                    <P style={styles.errorText}>Full name is required</P>
                  )}
                  <Input
                    label={`Sender’s Mobile number `}
                    placeholder="8144855058"
                    inputStyle={{ width: "80%" }}
                    contStyle={{ marginTop: 16 }}
                    value={phone}
                    onChangeText={(e) => setPhone(e)}
                    keyboardType={"numeric"}
                    error={error}
                    leftIcon={
                      <TouchableOpacity
                        style={[styles.pinInput]}
                      // onPress={() => {
                      //   setShow(true);
                      // }}
                      >
                        <Image
                          source={flag}
                          style={{
                            width: 24,
                            height: 24,
                            marginLeft: (14 / baseWidth) * width,
                            marginRight: (10 / baseWidth) * width,
                            borderRadius: 100,
                          }}
                        />
                        {/* @ts-ignore */}
                        <P style={[styles.pinTextInput, { fontSize: 12 }]}>
                          {mobileCode}
                        </P>
                      </TouchableOpacity>
                    }
                  />
                  {error && (
                    <P style={styles.errorText}>
                      Number should not be less than 4 digit
                    </P>
                  )}
                </>
              )}
              {serviceProvider.length > 0 && (
                <TouchableOpacity
                  onPress={() => {
                    setShow2(true);
                  }}
                  disabled={serviceProvider.length === 1 ? true : false}
                >
                  <View>
                    <Input
                      value={countryCode2}
                      label="Sender’s Service provider"
                      placeholder="Momo"
                      inputStyle={{ width: "65%", color: "#161817" }}
                      editable={false}
                      customInputStyle={{
                        backgroundColor:
                          serviceProvider.length === 1
                            ? colors.secBackground
                            : "transparent",
                        borderWidth: serviceProvider.length === 1 ? 0 : 1,
                      }}
                      contStyle={{
                        marginTop: 16,
                      }}
                      leftIcon={
                        <View>
                          <Image
                            source={moneyIcon}
                            style={{ width: 24, height: 24, marginLeft: 14 }}
                          />
                        </View>
                      }
                      rightIcon={
                        serviceProvider.length === 1 ? (
                          <></>
                        ) : (
                          <View
                            style={{
                              //   backgroundColor: "red",
                              width: "15%",
                              height: "100%",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <SvgXml xml={svg.dropDown} />
                          </View>
                        )
                      }
                    />
                  </View>
                </TouchableOpacity>
              )}
              {country != "" && (
                <Input
                  value={note}
                  label="Note(Optional)"
                  placeholder=""
                  onChangeText={(e) => setNote(e)}
                  inputStyle={{ width: "65%", color: "#161817" }}
                  contStyle={{
                    marginTop: 16,
                  }}
                />
              )}
            </View>
            <View style={{ width: "80%", alignSelf: "center", marginTop: 32 }}>
              <Button
                btnText="Next"
                onPress={() => {
                  if (gateWayError) {
                    setShowChannelError(true);
                  } else {
                    let errorHandling = 1;
                    if (phone.length < 4) {
                      setError(true);
                      errorHandling = 0;
                    } else {
                      setError(false);
                    }
                    if (fullName.length === 0) {
                      setNameError(true);
                      errorHandling = 0;
                    } else {
                      setNameError(false);
                    }

                    if (errorHandling === 1) {
                      navigation.navigate("AddMoneyAmountScreen2", {
                        country: country,
                        channelID: channelID,
                        currencyCode: currencyCode,
                        symbol: symbol,
                        networkID: networkID,
                        provider: countryCode2,
                        phone: `${mobileCode}${phone?.trim().replace("0", "")}`,
                        accName: fullName,
                        max: max,
                        min: min,
                        aplhaCode2: aplhaCode2,
                      });
                    } else {
                    }
                  }
                }}
                disabled={
                  country === "" ||
                  (serviceProvider.length > 0 && countryCode2 === "") ||
                  providerError
                }
              />
            </View>
          </View>
        </ScrollView>
      </Div>
      <BottomSheet
        isVisible={showCountries}
        showBackArrow={false}
        backspaceText="Select country"
        onClose={() => setShowCountries(false)}
        modalContentStyle={{ height: "75%" }}
        extraModalStyle={{ height: "73%" }}
        componentHolderStyle={{ flex: 1 }}
        components={
          <CountrySelect
            excludedCountries={NotMMCountires}
            onActiveCountryChange={handleActiveCountry}
            onActiveFlag={handleActiveFlag}
            onActiveYellowCard={handleActiveYellowCard}
            onActiveCurrencyCode={handleActiveCurrencyCode}
            onSymbolChange={handleActiveSymbol}
            onActiveMobileCodeChange={handleActiveMobileCode}
            onActiveAlphaCode2Change={handleActiveAplhaCode2}
            onPress={(index: number) => {
              // Important: Close the modal AFTER all the callbacks have been processed
              setTimeout(() => {
                setLoading(true);
                setShowCountries(false);
                setFullName("");
                setPhone("");
                setCountryCode2("");
                setProviderError(false); // Reset provider error when country changes
              }, 100);
            }}
          />
        }
      />

      <BottomSheet
        isVisible={show2}
        onClose={() => setShow2(false)}
        backspaceText="Provider"
        showBackArrow={false}
        modalContentStyle={{ height: "50%" }}
        extraModalStyle={{ height: "48%" }}
        components={
          <View>
            <P
              style={{
                marginTop: 24,
                fontSize: 12,
                lineHeight: 19.2,
                color: colors.gray,
              }}
            >
              Select your service provider
            </P>
            <View>
              {serviceProvider.map((item, index) => {
                return (
                  <ListItemSelect
                    key={index}
                    text1={item.name}
                    image={require("../../assets/momo.png")}
                    onPress={() => {
                      setIsProviderSelected(index);
                      setCountryCode2(item.name);
                      setNetworkID(item.id);
                      setShow2(false);
                    }}
                    containerStyle={{
                      marginBottom: 16,
                      marginTop: index == 0 ? 6 : 0,
                    }}
                    isActive={isproviderSelected == index}
                  />
                );
              })}
            </View>
          </View>
        }
      />
      <BottomSheet
        isVisible={showChannelError}
        showBackArrow={false}
        backspaceText=""
        onClose={() => setShowChannelError(false)}
        modalContentStyle={{ height: "55%" }}
        extraModalStyle={{ height: "53%" }}
        components={
          <View
            style={{
              width: "100%",
              alignItems: "center",
              justifyContent: "center",
              paddingTop: 34,
            }}
          >
            <SvgXml xml={svg.noCloud} width={44} height={44} />

            <P style={{ marginTop: 16 }}>Payment option unavailable!</P>
            <P
              style={{
                textAlign: "center",
                fontSize: 12,
                color: colors.gray,
                marginTop: 4,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              We apologize for any inconvenience this may cause and appreciate
              your patience while we work to enhance our payment options.
            </P>

            <View style={{ marginTop: 32 }}>
              <Button
                btnText="Explore other payment options"
                onPress={() => {
                  setShowChannelError(false);
                  navigation.pop();
                }}
              />
            </View>
          </View>
        }
      />
      <Loader loading={true} visible={loading} />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: "100%",
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 24,
  },
  pyItem: {
    width: "100%",
    borderRadius: 6,
    borderWidth: 1,
    marginBottom: 16,
    alignItems: "center",
    flexDirection: "row",
    padding: 16,
  },
  pyName: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
  },
  rate: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  noteCont: {
    width: "100%",
    padding: 16,
    paddingTop: 8,
    paddingBottom: 8,
    borderRadius: 8,
  },
  pinInput: {
    width: "35%",
    height: "100%",
    alignItems: "center",
    flexDirection: "row",
    borderRightColor: "#E6E5E5",
    borderRightWidth: 1,
  },
  errorText: {
    color: colors.red,
    fontSize: 10,
    marginTop: 5,
    fontFamily: fonts.poppinsRegular,
    // marginTop: -30,
  },
});
