import React, { useState, useRef, useEffect } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import NoteComponent from "../../components/NoteComponent";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import CountrySelect from "../../components/CountrySelect";
import Button from "../../components/Button";
import NoteComponent2 from "../../components/NoteComponent2";
import BarCodeScanner from "../../components/BarCodeScanner";
import { GetUserWallet, GetWalletById } from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import { useToast } from "../../context/ToastContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const WAValidator = require("multicoin-address-validator");

const baseHeight = 802;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function P2pScreen({ navigation, route }) {
  const [paymentType, setPaymentType] = useState(null);
  const [flag, setFlag] = useState(require("../../assets/turkey.png"));
  const [country, setCountry] = useState("");
  const [showCountries, setShowCountries] = useState(false);
  const [activeNetwork, setActiveNetwork] = useState(null);
  const [displayArrivalTime, setDisplayArrivalTime] = useState(null);
  const [showQrCode, setShowQrCode] = useState(false);
  const [wallets, setWallets] = useState<any>([]);
  const [waldetails, setWalDetails] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [depositAddress, setDepositAddress] = useState("");
  const [error, setError] = useState(false);
  const [chain, setChain] = useState("");
  const [allNetwork, setAllNetwork] = useState([]);
  const { data } = route?.params || "";
  const { asset } = route?.params || "";
  const [detectedNetwork, setDetectedNetwork] = useState("");
  const [netError, setNetError] = useState(false);
  const [isProd, setIsProd] = useState(true);
  const [matchedNetworks, setMatchedNetworks] = useState<any>([]);
  const { handleToast } = useToast();

  const net = isProd ? "testnest" : "mainnet";
  const chainNameMapping = {
    CHAIN_POLYGON: "POLYGON",
    CHAIN_AVALANCHE: "AVALANCHE",
    CHAIN_ARBITRUM: "ARBITRUM",
    CHAIN_BASE: "BASE",
    CHAIN_SOLANA: "SOLANA",
  };

  const dummyNetworks = [
    {
      name: "POLYGON",
      altName: "Polygon",
      arrival: "Money arrival in 2 minutes",
      chain: "matic",
      isRecommended: true,
      isComingSoon: false,
      icon: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/info/logo.png",
    },
    {
      name: "AVALANCHE",
      altName: "Avalanche - C Chain",
      arrival: "Money arrival in 13 minutes",
      chain: "avax",
      isRecommended: false,
      isComingSoon: false,
      icon: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/avalanchec/info/logo.png",
    },
    {
      name: "ARBITRUM",
      altName: "Arbitrum One",
      arrival: "Money arrival in 13 minutes",
      chain: "eth",
      isRecommended: false,
      isComingSoon: false,
      icon: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/arbitrum/info/logo.png",
    },
    {
      name: "BASE",
      altName: "Base",
      arrival: "Money arrival in 13 minutes",
      chain: "eth",
      isRecommended: false,
      isComingSoon: false,
      icon: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/base/info/logo.png",
    },
    {
      name: "SOLANA",
      arrival: "Coming soon",
      chain: "sol",
      isRecommended: false,
      isComingSoon: true,
      icon: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/solana/info/logo.png",
    },
  ];
  const getWallet = async () => {
    setLoading(true);
    try {
      const wallet = await await withApiErrorToast(GetUserWallet(), handleToast);
      if (wallet.wallets) {
        setLoading(false);
        setWallets(wallet.wallets);
        if (wallet.wallets.length === 1) {
          setPaymentType(wallet.wallets[0]);
        }
        // if (data != null || data != "" || data || undefined) {
        //   if (data.length === 42) {
        //     setPaymentType(wallet.wallets[0]);
        //     setDepositAddress(data);
        //   } else if (data.length === 34) {
        //     setPaymentType(wallet.wallets[1]);
        //     setDepositAddress(data);
        //   } else {
        //   }
        // }
      } else {
        setLoading(false);
        handleToast("Error getting wallet", "error");
      }
    } catch (error) { }finally{
      setLoading(false)
    }
  };

  const getWalletById = async () => {
    try {
      const walletDetails = await GetWalletById(paymentType.id);
      if (walletDetails.wallet) {
        setLoading(false);
        setWalDetails(walletDetails.wallet);
        setError(false);
      }
    } catch (error) { }
  };
  const getWalById = async () => {
    try {
      const walletDetails = await withApiErrorToast(GetWalletById(paymentType.id), handleToast);
      if (walletDetails.wallet) {
        setLoading(false);
        setWalDetails(walletDetails.wallet);
        const transformedAssets = walletDetails.wallet.assets.map((asset) => ({
          ...asset,
          chain: chainNameMapping[asset.chain] || asset.chain,
        }));
        setAllNetwork(transformedAssets);
        const firstAsset = transformedAssets[0];
      } else {
      }
    } catch (error) { }
  };

  useEffect(() => {
    getWalById();
  }, [paymentType]);

  useEffect(() => {
    getWallet();
    if (data) {
      handleAddressInput(data);
    }
    if (asset != "" && asset != null && asset != undefined) {
      setPaymentType(asset);
    }
  }, []);

  const handleScan = (index) => {
    if (index.length === 42) {
      handleAddressInput(index);
      setActiveNetwork(null);
    } else if (index.length === 34) {
      handleAddressInput(index);
      setActiveNetwork(null);
    }
  };
  const detectNetwork = (address) => {
    const availableNetworks = [];

    dummyNetworks.forEach((network) => {
      const isValid = WAValidator.validate(address, network.chain); // Validate per network
      if (isValid) {
        availableNetworks.push(network.name); // Add valid networks to the list
      }
    });
    return availableNetworks; // Return the list of available networks
  };

  const handleAddressInput = (input) => {
    setDepositAddress(input);
    const network = detectNetwork(input);
    if (network.length > 0) {
      setMatchedNetworks(network);
      setError(false);
      // setDisplayArrivalTime(
      //   networks.find((net) => net.name === network)?.arrival || null
      // );
    } else {
      setError(true); // Show an error for invalid address
      setDisplayArrivalTime(null);
    }
  };
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Wallet address" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <P
                style={{
                  fontSize: 12,
                  fontFamily: fonts.poppinsRegular,
                  marginBottom: 6,
                }}
              >
                Asset
              </P>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}
              >
                {wallets.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => {
                      if (data) {
                        setPaymentType(item);
                        setActiveNetwork(null);
                        setDetectedNetwork("");
                        setChain("");
                      } else {
                        setPaymentType(item);
                        setDepositAddress("");
                        setActiveNetwork(null);
                        setDetectedNetwork("");
                        setChain("");
                      }
                    }}
                    disabled={item.rate == "Coming soon.."}
                  >
                    <View
                      style={[
                        styles.pyItem,
                        {
                          borderColor:
                            paymentType === item || item?.asset === asset?.asset
                              ? colors.primary
                              : colors.stroke,
                          width:
                            item.length > 1
                              ? (126 / baseWidth) * width
                              : (80 * width) / 100,
                          // borderWidth: item.rate == "Coming soon.." ? 0 : 1,
                          // marginTop: item.rate == "Coming soon.." ? -16 : 0,
                          // marginBottom:
                          //   index == paymentTypes.length - 1 ? 0 : 16,
                        },
                      ]}
                    >
                      <SvgXml
                        xml={item.asset === "USDT" ? svg.tather : svg.usdCoin}
                      />
                      <View style={{ marginLeft: 8 }}>
                        <P style={styles.pyName}>
                          {item.asset === "USDT" ? "Tether" : "USD coin"}
                        </P>
                        {/* <P style={styles.rate}>{item.rate}</P> */}
                      </View>
                      {paymentType === item && (
                        <SvgXml
                          xml={svg.ppTick}
                          style={{ position: "absolute", right: 16 }}
                        />
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
              <Input
                value={depositAddress}
                placeholder="Enter wallet address"
                label={"Wallet address"}
                contStyle={{ marginTop: (16 / baseHeight) * height }}
                onChangeText={(text) => handleAddressInput(text)}
                inputStyle={{ paddingLeft: 16, paddingRight: 16 }}
                error={error}
                rightIcon={
                  <TouchableOpacity
                    onPress={() => {
                      setShowQrCode(true);
                    }}
                    style={{
                      width: 25,
                      height: 25,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <SvgXml xml={svg.qrcode} pointerEvents="none" />
                  </TouchableOpacity>
                }
              />

              {error && (
                <P style={styles.errorText}>
                  Invalid {waldetails.asset} address
                </P>
              )}
              {waldetails != null &&
                waldetails != undefined &&
                waldetails.length != 0 && (
                  <>
                    <TouchableOpacity
                      onPress={() => {
                        setShowCountries(true);
                      }}
                    // disabled={waldetails?.assets?.length === 1}
                    // disabled={true}
                    >
                      <Input
                        // value={
                        //   waldetails?.assets[0]?.chain === "CHAIN_CELO"
                        //     ? "Celo"
                        //     : waldetails?.assets[0]?.chain === "CHAIN_TRON"
                        //     ? "Tron"
                        //     : ""
                        // }
                        value={chain}
                        label="Network"
                        placeholder="Select network"
                        inputStyle={{ width: "65%", color: "#161817" }}
                        contStyle={{ marginTop: (16 / baseHeight) * height }}
                        // customInputStyle={{
                        //   backgroundColor:
                        //     waldetails?.assets?.length === 1
                        //       ? "rgba(241, 235, 255, 1)"
                        //       : "transparent",
                        // }}
                        editable={false}
                        rightIcon={
                          <View
                            style={{
                              //   backgroundColor: "red",
                              width: "15%",
                              height: "100%",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <SvgXml xml={svg.dropDown} />
                          </View>
                        }
                        error={netError}
                      />
                    </TouchableOpacity>
                    {/* <View style={{ marginTop: 6 }}>
                      <NoteComponent2
                        text={
                          waldetails?.assets[0]?.chain === "CHAIN_CELO"
                            ? "Money arrival in 3 minutes "
                            : "Money arrival in 5 minutes"
                        }
                      />
                    </View> */}
                    {netError && (
                      <P style={styles.errorText}>Select a network</P>
                    )}
                  </>
                )}

              {/* {displayArrivalTime && (
                <View style={{ marginTop: 6 }}>
                  <NoteComponent2 text={displayArrivalTime} />
                </View>
              )} */}
            </View>
            <View style={{ width: "80%", alignSelf: "center", marginTop: 32 }}>
              <Button
                btnText="Next"
                // onPress={handleValidation}
                onPress={() => {
                  if (
                    paymentType.asset === "USDT" &&
                    depositAddress.length < 42
                  ) {
                    setError(true);
                  } else if (
                    paymentType.asset === "USDC" &&
                    depositAddress.length < 34
                  ) {
                    setError(true);
                  } else if (activeNetwork === "" || activeNetwork === null) {
                    setNetError(true);
                  } else {
                    setError(false);
                    setNetError(false);
                    navigation.navigate("P2pAmountScreen", {
                      walletdetails: waldetails,
                      depositAddress: depositAddress,
                      chain: chain,
                    });
                  }
                }}
                disabled={paymentType === null}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
      <BottomSheet
        isVisible={showCountries}
        showBackArrow={false}
        backspaceText="Network"
        onClose={() => setShowCountries(false)}
        modalContentStyle={{ height: dummyNetworks.length < 2 ? "45%" : "75%" }}
        extraModalStyle={{ height: dummyNetworks.length < 2 ? "43%" : "73%" }}
        components={
          <ScrollView
            contentContainerStyle={{ paddingBottom: 300 }}
            showsVerticalScrollIndicator={false}
          >
            <View style={{ paddingTop: 24 }}>
              <P
                style={{
                  fontSize: 12,
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                select a blockchain to send USDC
              </P>
              <View>
                {dummyNetworks.map((item, index) => {
                  const isMatched = matchedNetworks.includes(item.name);
                  return (
                    <TouchableOpacity
                      key={index}
                      disabled={item.isComingSoon || !isMatched}
                      onPress={() => {
                        if (!item.isComingSoon && isMatched) {
                          setActiveNetwork(item.name);
                          setChain(item.name);
                          setShowCountries(false);
                        }
                      }}
                    >
                      <View
                        style={{
                          width: "100%",
                          padding: 16,
                          paddingTop: 8,
                          paddingBottom: 8,
                          borderRadius: 8,
                          marginBottom: 16,
                          marginTop: 6,
                          justifyContent: "center",
                          backgroundColor:
                            activeNetwork === item.name
                              ? colors.lowOpPrimary2
                              : "transparent",
                          opacity: item.isComingSoon ? 0.6 : 1,
                        }}
                      >
                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                            marginBottom: 4,
                          }}
                        >
                          <Image
                            source={{ uri: item.icon }}
                            style={{
                              width: 24,
                              height: 24,
                              borderRadius: 12,
                              marginRight: 12,
                            }}
                            resizeMode="contain"
                          />
                          <P style={{ fontSize: 12, lineHeight: 18, flex: 1 }}>
                            {item.altName}{" "}
                            {item.isRecommended && (
                              <P
                                style={{
                                  fontSize: 10,
                                  fontFamily: fonts.poppinsMedium,
                                }}
                              >
                                (Recommended)
                              </P>
                            )}
                          </P>
                        </View>
                        <P
                          style={{
                            fontSize: 12,
                            color: item.isComingSoon
                              ? colors.gray
                              : colors.gray,
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          {item.arrival}
                        </P>
                        {!item.isComingSoon && (
                          <View
                            style={{
                              minWidth: 72,
                              alignItems: "center",
                              justifyContent: "center",
                              paddingTop: 4,
                              paddingBottom: 4,
                              paddingRight: 10,
                              paddingLeft: 10,

                              position: "absolute",
                              right: 10,
                              top: 8,
                              borderRadius: 100,
                              backgroundColor: isMatched
                                ? colors.primary
                                : colors.secBackground,
                            }}
                          >
                            <P
                              style={{
                                fontSize: 10,
                                color: isMatched ? colors.white : colors.gray,
                              }}
                            >
                              {isMatched ? "Matched" : "Unmatched"}
                            </P>
                          </View>
                        )}
                        {item.isComingSoon && (
                          <View
                            style={{
                              minWidth: 80,
                              alignItems: "center",
                              justifyContent: "center",
                              paddingTop: 4,
                              paddingBottom: 4,
                              paddingRight: 10,
                              paddingLeft: 10,
                              position: "absolute",
                              right: 0,
                              top: 8,
                              borderRadius: 100,
                              backgroundColor: colors.orange || "#FFA500",
                            }}
                          >
                            <P
                              style={{
                                fontSize: 10,
                                color: colors.white,
                                fontFamily: fonts.poppinsMedium,
                              }}
                            >
                              Coming Soon
                            </P>
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                  );
                })}
                <View style={{ marginTop: 16 }}>
                  <NoteComponent2
                    text={`Please note that minimum Deposit/ withdrawal is $1 USDC. Always make sure you confirm asset and Network`}
                  />
                </View>
              </View>
            </View>
          </ScrollView>
        }
      />
      {loading && <Loader />}
      <BarCodeScanner
        visible={showQrCode}
        onClose={() => setShowQrCode(false)}
        onScan={handleScan}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: "100%",
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 24,
  },
  pyItem: {
    borderRadius: 8,
    borderWidth: 1,
    // marginBottom: 16,
    alignItems: "center",
    flexDirection: "row",
    paddingTop: (10 / baseHeight) * height,
    paddingBottom: (10 / baseHeight) * height,
    paddingLeft: (14 / baseWidth) * width,
    paddingRight: (14 / baseWidth) * width,
    // padding: 16,
  },
  pyName: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
  },
  rate: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  noteCont: {
    width: "100%",
    padding: 16,
    paddingTop: 8,
    paddingBottom: 8,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
});
