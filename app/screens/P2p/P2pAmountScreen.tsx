import React, { useEffect, useState } from "react";
import { View, StyleSheet, Dimensions, ScrollView, Image } from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import InputCard from "../../components/InputCard";
import Keyboard from "../../components/Keyboard";
import Button from "../../components/Button";
import { colors } from "../../config/colors";
import { runOnJS } from "react-native-reanimated";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import { GetNetworkFee } from "../../RequestHandlers/Wallet";
import { useToast } from "../../context/ToastContext";
import { formatToTwoDecimals } from "../../Utils/numberFormat";

const baseHeight = 820;
const { width, height } = Dimensions.get("window");
export default function P2pAmountScreen({ navigation, route }) {
  const [inputValue, setInputValue] = useState("0");
  const [error, setError] = useState(false);
  const { walletdetails } = route?.params || {};
  const { depositAddress } = route?.params || "";
  const { chain } = route?.params || "";
  const [isUsdInput, setIsUsdInput] = useState(true);
  const ngnRate = 1; // Example exchange rate
  const [networkFee, setNetworkFee] = useState("");
  const { handleToast } = useToast();

  const formatNumber = (value) => {
    value = value.toString();
    return value.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const formatNumberWithDecimal = (value, decimalPlaces = 2) => {
    if (!isNaN(value)) {
      return Number(value)
        .toFixed(decimalPlaces)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    return "0.00";
  };

  const handleKeyPress = (key) => {
    setError(false); // Reset error state on key press

    if (key === "←") {
      setInputValue((prev) => (prev.length > 1 ? prev.slice(0, -1) : "0"));
    } else if (key === "Enter") {
      // Handle enter key press
    } else {
      setInputValue((prev) => {
        let newValue = prev === "0" && key !== "." ? key : prev + key;
        newValue = newValue.replace(/[^0-9.]/g, ""); // Remove any non-numeric characters
        return newValue;
      });
    }
  };

  const toggleCurrency = () => {
    setIsUsdInput(!isUsdInput);
    setInputValue("0"); // Reset input value when toggling
  };

  const convertedValue = isUsdInput
    ? formatNumberWithDecimal(Number(inputValue) * ngnRate)
    : formatNumberWithDecimal(Number(inputValue) / ngnRate);

  function truncateWalletAddress(address) {
    if (address.length < 10) return address;
    const start = address.slice(0, 4);
    const end = address.slice(-4);
    return `${start}****${end}`;
  }

  const getNetworkFee = async () => {
    try {
      const res = await  GetNetworkFee(`CHAIN_${chain}`);
      setNetworkFee(res.fee);
    } catch (error) {}
  };
  useEffect(() => {
    getNetworkFee();
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Amount" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.inputCardWrap}>
              <View style={{ alignItems: "center", marginBottom: 16 }}>
                {/* <Image
                  source={walletdetails?.asset === 'USDT'? require("../../assets/tether.png")}
                  style={{ width: 32, height: 32, marginBottom: 8 }}
                /> */}
                <SvgXml
                  xml={
                    walletdetails?.asset == "USDT" ? svg.tather : svg.usdCoin
                  }
                  width={32}
                  height={32}
                />
                <P style={{ fontSize: 12 }}>
                  {walletdetails?.asset == "USDT" ? "Tether" : "USD coin"}
                </P>
                <P
                  style={{
                    fontSize: 12,
                    color: colors.gray,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  {truncateWalletAddress(depositAddress)} |{" "}
                  {/* {walletdetails?.assets[0]?.chain === "CHAIN_CELO"
                    ? "Celo"
                    : walletdetails?.assets[0]?.chain === "CHAIN_TRON"
                    ? "Tron"
                    : ""} */}
                  {chain}
                </P>
              </View>
              <InputCard
                headerText="How much do you want to send"
                onTogglePress={toggleCurrency}
                toggleStyle={{ top: "45%" }}
                amountValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 32,
                        lineHeight: 48,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `$${formatNumber(inputValue)}`
                        : `${formatNumber(inputValue)}`}
                      {""}
                      <P style={{ lineHeight: 48 }}>
                        {isUsdInput ? "USD" : walletdetails?.asset}
                      </P>
                    </P>
                  </>
                }
                convertedValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 16,
                        lineHeight: 24,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `${formatToTwoDecimals(
                            Number(inputValue) * ngnRate
                          )}`
                        : `$${formatToTwoDecimals(
                            Number(inputValue) / ngnRate
                          )}`}{" "}
                      <P style={{ lineHeight: 24, fontSize: 12 }}>
                        {isUsdInput ? walletdetails?.asset : "USD"}
                      </P>
                    </P>
                  </>
                }
                text1={`Network fee: ${networkFee} USD`}
                text2={`Available balance: ${formatToTwoDecimals(Number(walletdetails?.balance))} ${walletdetails?.asset}`}
                error={error}
              />
            </View>
            <View style={styles.bottom}>
              <View style={{ width: "90%", alignSelf: "center" }}>
                <Keyboard onKeyPress={handleKeyPress} />
              </View>
              <View
                style={{
                  width: "80%",
                  alignSelf: "center",
                  marginTop: 16,
                }}
              >
                <Button
                  btnText="Next"
                  onPress={() => {
                    inputValue == "0"
                      ? setError(true)
                      : Number(inputValue) < 1
                      ? handleToast("Minimum withdrawable amount is 10 USDC")
                      : parseFloat(inputValue) + Number(networkFee) >
                        parseFloat(walletdetails?.balance)
                      ? handleToast("Insufficient balance", "error")
                      : chain === "TRON" && Number(inputValue) < 10
                      ? handleToast("Minimum withdrawable amount is 10 USDT.")
                      : navigation.navigate("P2pConfirmDetails", {
                          inputValue: inputValue,
                          walletDetails: walletdetails,
                          depositAddress: depositAddress,
                          ngnRate: ngnRate,
                          chain: chain,
                          fee: networkFee,
                        });
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (92 * height) / 100,
    backgroundColor: "rgba(247, 244, 255, 1)",
    paddingTop: 16,
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
  },
  bottom: {
    width,
    flex: 1,
    justifyContent: "flex-end",
    paddingBottom: Math.max(5, height * 0.12),
    marginTop: 16,
  },
});
