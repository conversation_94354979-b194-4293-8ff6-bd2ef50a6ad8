import React, { useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import SendMoneyStatus from "../../components/SeendMoneyStatus";
import Content2 from "../../components/Content2";
import CustomSwitch from "../../components/CustomSwitch";
import NoteComponent2 from "../../components/NoteComponent2";
import { formatToTwoDecimals } from "../../Utils/numberFormat";

const baseHeight = 802;
const { width, height } = Dimensions.get("window");
export default function P2pConfirmDetails({ navigation, route }) {
  const [showSendStatus, setShowSendStatus] = useState(false);
  const [selectedAcc, setSelectedAcc] = useState(null);
  const { depositAddress, inputValue, ngnRate, fee } = route?.params || "";
  const { walletDetails } = route?.params || {};
  const { chain } = route?.params || {};
  const accounts = [
    {
      id: 1,
      balance: 3500,
      currency: "USDT",
      exchangeRate: "1 USDT ~ 1 USD",
      type: "Tether",
    },
    {
      id: 2,
      balance: 0,
      currency: "USDT",
      exchangeRate: "1 USD ~ 1 USD",
      type: "USDC",
    },
  ];
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction details"
          navigation={navigation}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <DetailCard
                headText={"Money you’re sending"}
                amount={
                  <>
                    <P style={{ fontSize: 32, lineHeight: 48, marginRight: 2 }}>
                      ${formatToTwoDecimals(Number(inputValue))}
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                convertedAmount={
                  <>
                    <P style={{ fontSize: 16, lineHeight: 24, marginRight: 2 }}>
                      {formatToTwoDecimals(Number(inputValue) * ngnRate)}
                    </P>
                    <P
                      style={{
                        // marginTop: 5,
                        fontSize: 12,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      {walletDetails?.asset}
                    </P>
                  </>
                }
                lineStyle={{ borderStyle: "dashed" }}
                bottomComponent={
                  <View style={styles.desCont}>
                    <View style={styles.items}>
                      <P style={styles.holder}>Asset</P>
                      <P style={styles.value}>
                        {walletDetails?.asset == "USDT" ? "Tether" : "USD coin"}
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Network</P>
                      <P style={styles.value}>
                        {/* {walletDetails?.assets[0]?.chain === "CHAIN_CELO"
                          ? "Celo"
                          : walletDetails?.assets[0]?.chain === "CHAIN_TRON"
                          ? "Tron"
                          : ""} */}
                        {chain}
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Wallet address</P>
                      <P style={styles.value}>{depositAddress}</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Fee</P>
                      <P style={styles.value}>
                        {fee}{" "}
                        <P
                          // @ts-ignore
                          style={[
                            styles.value,
                            { fontFamily: fonts.poppinsRegular },
                          ]}
                        >
                          USD
                        </P>
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Exchange rate</P>
                      <P style={styles.value}>
                        1 {walletDetails?.asset} ~1 USD
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Payment method</P>
                      <P style={styles.value}>Send money</P>
                    </View>
                    <View style={[styles.items, { marginBottom: 0 }]}>
                      <P style={styles.holder}>Type</P>
                      <P style={styles.value}>Wallet address</P>
                    </View>
                    <View style={styles.line}></View>

                    <P
                      style={{
                        color: "#A5A1A1",
                        fontSize: 12,
                        textAlign: "center",
                        marginBottom: 6,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      Select Payment method
                    </P>
                    {/* <View
                      style={{
                        width: "100%",
                        flexDirection: "row",
                        justifyContent: "space-between",
                        marginBottom: 16,
                      }}
                    >
                      <P style={{ color: "#A5A1A1", fontSize: 12 }}>
                        SFx point
                      </P>
                      <View style={{ flexDirection: "row" }}>
                        <P style={{ marginRight: 8 }}>$50</P>
                        <CustomSwitch />
                      </View>
                    </View> */}
                    <View style={{ marginBottom: 16, width: "98%" }}>
                      <NoteComponent2
                        text={`You can only send money from your ${
                          walletDetails?.asset === "USDT"
                            ? "tether"
                            : "usd coin"
                        } (${walletDetails?.asset}) account`}
                      />
                    </View>

                    <Content2
                      svgg={
                        walletDetails?.asset === "USDT"
                          ? svg.tather
                          : svg.usdCoin
                      }
                      // onclick={index === selectedAcc}
                      // ClickedMe={() => {
                      //   if (selectedAcc === index) {
                      //     setSelectedAcc(null);
                      //   } else if (item.balance > 0) {
                      //     setSelectedAcc(index);
                      //   }
                      // }}
                      onclick={true}
                      header={
                        <>
                          <P
                            style={{
                              fontSize: 12,
                              lineHeight: 18,
                              fontFamily: fonts.poppinsMedium,
                            }}
                          >
                            {walletDetails?.balance}
                          </P>
                          <P
                            style={{
                              fontSize: 12,
                              lineHeight: 18,
                              fontFamily: fonts.poppinsRegular,
                              color: colors.gray,
                            }}
                          >
                            {" "}
                            {walletDetails?.asset}
                          </P>
                        </>
                      }
                      body={`1 ${walletDetails?.asset} ~ 1 USD`}
                      containerStyle={{
                        justifyContent: "flex-start",
                        paddingLeft: 16,
                        marginBottom: 16,
                        borderRadius: 10,
                        borderColor: "red",
                        backgroundColor: colors.lowOpPrimary2,
                      }}
                      itemWrapper={{ marginLeft: 8 }}
                      headerStyle={{ marginBottom: 4 }}
                      textStyle={{
                        fontFamily: fonts.poppinsRegular,
                        fontSize: 12,
                      }}
                      rightComponent={
                        <SvgXml
                          xml={svg.purple_check}
                          style={{ position: "absolute", right: "5%" }}
                        />
                      }
                    />
                  </View>
                }
              />
              <View style={styles.buttonWrap}>
                <Button
                  btnText="Confirm"
                  onPress={() => {
                    navigation.navigate("P2pTransactionPin", {
                      depositAddress: depositAddress,
                      walletDetails: walletDetails,
                      inputValue: inputValue,
                      chain: chain,
                    });
                    // setShowSendStatus(true);
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    // height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
    width: 120,
    textAlign: "right",
  },
  buttonWrap: {
    // backgroundColor: 'red',
    width: "80%",
    alignSelf: "center",
    marginTop: (32 / baseHeight) * height,
    paddingBottom: (40 / baseHeight) * height,
  },
  line: {
    width: "100%",
    // height: 1,
    borderBottomWidth: 1,
    borderStyle: "dashed",
    borderColor: colors.stroke,
    marginTop: (3.5 * height) / 100,
    marginBottom: (3.5 * height) / 100,
  },
});
