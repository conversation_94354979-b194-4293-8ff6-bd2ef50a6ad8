import React, { useEffect, useState, useCallback } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  StatusBar,
  Modal,
  BackHandler,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { colors } from "../../../config/colors";
import P from "../../../components/P";
import { fonts } from "../../../config/Fonts";
import Button from "../../../components/Button";
import Link from "../../../components/Link";
import {
  useNavigation,
  useFocusEffect,
  CommonActions,
} from "@react-navigation/native";
import { GetTransationById } from "../../../RequestHandlers/Wallet";
import Loader from "../../../components/ActivityIndicator";
import Div from "../../../components/Div";
import H4 from "../../../components/H4";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";

interface PProps {
  okayPress?: any;
  viewDetailPress?: any;
  tranStat?: "failed" | "success" | "pending";
  visible?: true | false;
  requestClose?: any;
  from?: string;
}
const { width, height } = Dimensions.get("window");
export default function OTCTransactionStatus({ navigation, route }) {
  const [stImg, setStImg] = useState(
    require("../../../assets/alert-circle.png")
  );
  const [stText, setStText] = useState("Sent money is pending");
  const [tranStat, setTranState] = useState("pending");
  const { response } = route?.params || {};
  const [tranDetails, setTranDetails] = useState<any>([]);
  const [yellowCardData, setYellowCardData] = useState<any>([]);
  const [loader, setLoader] = useState(false);

  // const [tranStat, setTranStat] = useState("pending");

  const getTransaction = async () => {
    try {
      const id =
        response.id === undefined ? response?.transaction?.id : response?.id;
      const transaction = await GetTransationById(id);
      if (transaction) {
        setLoader(false);
      }
      if (transaction.transaction) {
        setTranDetails(transaction.transaction);
      }
      if (transaction.yellowCardData) {
        setYellowCardData(transaction.yellowCardData);
      }
      transaction.transaction.status === "completed"
        ? setTranState("success")
        : transaction.transaction.status === "failed"
        ? setTranState("failed")
        : setTranState("pending");
    } catch (error) {
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    if (tranStat === "failed") {
      setStImg(require("../../../assets/cancel-circle.png"));
      setStText("Sent money failed");
    } else if (tranStat === "success") {
      setStImg(require("../../../assets/success.png"));
      setStText("Money successfully sent");
    } else {
      setStImg(require("../../../assets/alert-circle.png"));
    }
  }, [tranStat]);

  useEffect(() => {
    setLoader(true);
    if (response) {
      getTransaction();
    }
    const interval = setInterval(() => {
      getTransaction();
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Handle back navigation
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        // Reset navigation stack to avoid loading issues
        // @ts-ignore
        // navigation.navigate("BottomTabNavigator");
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "BottomTabNavigator" }],
          })
        );
        return true;
      };
      // Disable iOS swipe back gesture
      navigation.setOptions({
        gestureEnabled: false,
      });
      // Handle Android back button
      BackHandler.addEventListener("hardwareBackPress", onBackPress);

      return () => {
        BackHandler.removeEventListener("hardwareBackPress", onBackPress);
      };
    }, [navigation])
  );

  if (tranStat === "success") {
    return (
      <View style={{ backgroundColor: colors.white, flex: 1 }}>
        <Div>
          <View
            style={{ paddingHorizontal: 24, marginTop: (10 * height) / 100 }}
          >
            <View style={styles.whatsAppIconContainer}>
              <Image
                source={require("../../../assets/celeb.png")}
                style={{ width: 80, height: 80 }}
              />
            </View>
            <H4 style={styles.whatsAppTitle}>
              Money successfully sent{"\n"}Proceed to SFx OTC deck
            </H4>
            <P style={styles.whatsAppSubtitle}>
              To complete your OTC transaction, please continue on WhatsApp
            </P>
            <View style={styles.checkItemContainer}>
              <SvgXml xml={svg.checkGreen} width={20} height={20} />
              <P style={styles.checkItemText}>
                Share your request with otc on whatsapp{" "}
                <P
                  style={{
                    fontFamily: fonts.poppinsRegular,
                    fontSize: 12,
                    textDecorationLine: "underline",
                  }}
                >
                  +905338833199
                </P>{" "}
                is the only OTC channel
              </P>
            </View>
            <View style={styles.checkItemContainer}>
              <SvgXml xml={svg.checkGreen} width={20} height={20} />
              <P style={styles.checkItemText}>
                Receive USD in your SFx wallet after transacting with OTC
              </P>
            </View>
            <View style={styles.reminderContainer}>
              <SvgXml xml={svg.infoCircleOutLine} width={16} height={16} />
              <View>
                <View style={styles.reminderHeader}>
                  <P style={styles.reminderTitle}>Reminder:</P>
                </View>
                <P style={styles.reminderItem}>• Minimum amount: $1 USD</P>
                <P style={styles.reminderItem}>
                  • Upload your payment receipt for confirmation
                </P>
                <P style={styles.reminderItem}>
                  • Your wallet will be credited after verification
                </P>
              </View>
            </View>
            <View style={{ width: "80%", alignSelf: "center" }}>
              <Button
                btnText="Send receipt to OTC deck"
                style={styles.whatsAppButton}
              />
            </View>
          </View>
        </Div>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.body}
    >
      <View style={styles.itemBox}>
        <Image source={stImg} style={{ width: 64, height: 64 }} />
        <P style={styles.statusState}>{stText}</P>
        {tranStat === "failed" ? (
          <P style={styles.stTx}>
            Money sent failed due to technical issue,{"\n"}please try again
            later!
          </P>
        ) : tranStat === "success" ? (
          <P style={styles.stTx}>
            You have successfully sent {tranDetails?.amount} USD to{"\n"}
            {tranDetails?.internalTransferReceiver
              ? `${tranDetails?.internalTransferReceiver?.firstName} ${tranDetails?.internalTransferReceiver?.lastName}`
              : yellowCardData?.destination?.accountName
              ? yellowCardData?.destination?.accountName
              : tranDetails?.toWallet}
          </P>
        ) : (
          <P style={styles.stTx}>
            Money is processing, please check {"\n"}money status later!
          </P>
        )}

        <View style={{ width: "75%", marginTop: 32 }}>
          <Button
            btnText="Okay!"
            onPress={() => {
              setLoader(false);
              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [{ name: "BottomTabNavigator" }],
                })
              );
            }}
          />
          <Link
            style={{ textAlign: "center", marginTop: 16, fontSize: 12 }}
            onPress={() => {
              console.log(tranDetails);
              const transactionType =
                tranDetails?.type === "internal-tranfer"
                  ? "sfx money app"
                  : tranDetails?.paymentGayway === "momo"
                  ? "mobile money"
                  : tranDetails?.paymentGayway === "bank"
                  ? "bank transfer"
                  : tranDetails?.provider === "circle"
                  ? "p2p"
                  : "unknown";
              const id =
                response?.id === undefined
                  ? response?.transaction?.id
                  : response?.id;
              navigation.navigate("AllTransactionDetails", {
                id: id,
                transactionType: transactionType,
              });
            }}
          >
            View details
          </Link>
        </View>
      </View>
      {loader && <Loader />}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    height: (105 * height) / 100,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    bottom: 0,
    // top: 0,
    zIndex: 100,
  },
  itemBox: {
    width: "100%",
    alignItems: "center",
    // marginTop: (20*height)/100
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
  whatsAppModalContent: {
    paddingTop: 24,
    paddingBottom: 24,
  },
  whatsAppIconContainer: {
    alignItems: "center",
    marginBottom: 16,
  },
  whatsAppTitle: {
    fontSize: 18,
    fontFamily: fonts.poppinsBold,
    textAlign: "center",
    marginBottom: 8,
  },
  whatsAppSubtitle: {
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center",
    color: colors.gray,
    marginBottom: 24,
  },
  checkItemContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  checkItemText: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    marginLeft: 8,
    flex: 1,
  },
  reminderContainer: {
    backgroundColor: colors.redSubtle,
    borderLeftWidth: 4,
    borderColor: colors.red,
    padding: 16,
    borderRadius: 8,
    marginVertical: 16,
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  reminderHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  reminderTitle: {
    fontSize: 10,
    fontFamily: fonts.poppinsMedium,
    // marginLeft: 8,
  },
  reminderItem: {
    fontSize: 10,
    fontFamily: fonts.poppinsRegular,
    // marginBottom: 4,
  },
  whatsAppButton: {
    marginTop: 16,
  },
  topSection: {
    width: "100%",
    flex: 1,
    justifyContent: "flex-start",
  },
  bottomSection: {
    width: "100%",
    flex: 1,
    justifyContent: "flex-end",
    paddingBottom: Math.max(5, height * 0.02),
  },
});
