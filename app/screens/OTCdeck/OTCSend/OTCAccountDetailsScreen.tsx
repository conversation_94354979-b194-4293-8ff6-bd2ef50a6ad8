import React, { use<PERSON><PERSON>back, useContext, useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../../config/Fonts";
import Div from "../../../components/Div";
import AuthenticationHedear from "../../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import P from "../../../components/P";
import MicroBtn from "../../../components/MicroBtn";
import { colors } from "../../../config/colors";
import DetailCard from "../../../components/DetailCard";
import Button from "../../../components/Button";
import Content from "../../../components/Content";
import Content2 from "../../../components/Content2";
import Input from "../../../components/Input";
import { Switch } from "react-native-gesture-handler";
import CustomSwitch from "../../../components/CustomSwitch";
import BarCodeScanner from "../../../components/BarCodeScanner";
import { SendSFXMoneyApp } from "../../../RequestHandlers/Wallet";
import {
  AddBeneficiary,
  GetBeneficiaries,
  GetUserByName,
} from "../../../RequestHandlers/User";
import ContentLoader, { Rect } from "react-content-loader/native";
import Loader from "../../../components/ActivityIndicator";
import CustomSwitch1 from "../../../components/CustomSwitch1";
import { useFocusEffect } from "@react-navigation/native";
import { ensureHttps } from "../../../components/AddHttp";
import { useToast } from "../../../context/ToastContext";
import { withApiErrorToast } from "../../../Utils/withApiErrorToast";
import { CredentailsContext } from "../../../RequestHandlers/CredentailsContext";
import NoteComponent2 from "../../../components/NoteComponent2";

const { width, height } = Dimensions.get("window");

export default function OTCAccountDetailScreen({ navigation, route }) {
  const [isEnabled, setIsEnabled] = useState(false);
  const { selectedMethod, isAddMoney } = route?.params || {};
  const { handleToast } = useToast();
  const [showQrCode, setShowQrCode] = useState(false);
  const [bene, setBene] = useState(false);
  const [accountNumberError, setAccountNumberError] = useState("");
  const [bankNameError, setBankNameError] = useState("");
  const [accountNameError, setAccountNameError] = useState("");
  const [loadng, setLoading] = useState(false);
  const [accountNumber, setAccountNumber] = useState("");
  const [bankName, setBankName] = useState("");
  const [accountName, setAccountName] = useState("");
  const [note, setNote] = useState("");
  const [details, setdetails] = useState<any>([]);
  const { data } = route?.params || "";
  const [isKycDone, setIsKycDone] = useState("false");
  const [loader, setLoader] = useState(false);
  const [ld, setLd] = useState(false);
  const { storedCredentails } = useContext(CredentailsContext);
  const [selectedAccountOption, setSelectedAccountOption] = useState("UBAN");
  const accountOption = ["UBAN", "IBAN", "Account number"];

  const isEmpty = (string: string) => {
    if (
      string == "" ||
      string == " " ||
      string == null ||
      string == undefined
    ) {
      return false;
    } else {
      return true;
    }
  };
  const handleAccountNumberChange = (text: string) => {
    setAccountNumber(text);
    setAccountNumberError(""); // Clear field-specific error
  };

  const handleBankNameChange = (text: string) => {
    setBankName(text);
    setBankNameError(""); // Clear field-specific error
  };

  const handleAccountNameChange = (text: string) => {
    setAccountName(text);
    setAccountNameError(""); // Clear field-specific error
  };

  const handleScan = (scannedData: string) => {
    setAccountNumber(scannedData);
  };

  // Handle account type selection change
  const handleAccountTypeChange = (selectedType: string) => {
    // Clear all form data when account type changes
    setAccountNumber("");
    setBankName("");
    setAccountName("");

    // Clear all error states
    setAccountNumberError("");
    setBankNameError("");
    setAccountNameError("");

    // Update selected account option
    setSelectedAccountOption(selectedType);
  };

  // Validation function
  const validateForm = () => {
    let isValid = true;

    // Reset all errors
    setAccountNumberError("");
    setBankNameError("");
    setAccountNameError("");

    // Validate account number
    if (!accountNumber.trim()) {
      setAccountNumberError(`${selectedAccountOption} is required`);
      isValid = false;
    } else if (accountNumber.trim().length < 3) {
      setAccountNumberError(
        `${selectedAccountOption} must be at least 3 characters`
      );
      isValid = false;
    }

    // Validate bank name
    if (!bankName.trim()) {
      setBankNameError("Bank name is required");
      isValid = false;
    } else if (bankName.trim().length < 2) {
      setBankNameError("Bank name must be at least 2 characters");
      isValid = false;
    }

    // Validate account name
    if (!accountName.trim()) {
      setAccountNameError("Account name is required");
      isValid = false;
    } else if (accountName.trim().length < 2) {
      setAccountNameError("Account name must be at least 2 characters");
      isValid = false;
    }

    return isValid;
  };

  // Handle navigation to OTC Transaction Screen
  const handleNext = () => {
    if (validateForm()) {
      setLd(true);

      // Prepare bank details data
      const bankDetails = {
        accountType: selectedAccountOption,
        accountNumber: accountNumber.trim(),
        bankName: bankName.trim(),
        accountName: accountName.trim(),
      };

      // Navigate to OTC Transaction Screen with all required data
      setTimeout(() => {
        setLd(false);
        navigation.navigate("OTCTransactionScreen", {
          selectedMethod,
          isAddMoney: isAddMoney || false,
          bankDetails,
          // Pass any additional data from route params
          ...route.params,
        });
      }, 500);
    } else {
      handleToast("Please fill all the details", "error");
    }
  };

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Bank details" navigation={navigation} />
        <View>
          <ScrollView automaticallyAdjustKeyboardInsets={true}>
            <View style={{ width: "90%", alignSelf: "center" }}>
              <NoteComponent2
                type="red"
                contStyle={{ backgroundColor: colors.white, marginTop: 16 }}
                text={
                  "Please fill in the bank details of the account you want to transfer funds to using the OTC Deck.This helps us process your request accurately and without delays."
                }
              />
            </View>
            <View style={styles.contentBody}>
              <View style={styles.detailWrap}>
                <View style={{ width: "100%" }}>
                  <P style={[styles.label, { marginBottom: 6 }]}>
                    Bank Details
                  </P>
                  {accountOption.map((item, index) => (
                    <TouchableOpacity
                      key={index}
                      onPress={() => handleAccountTypeChange(item)}
                      style={{
                        width: "100%",
                        height: 44,
                        borderWidth: 1,
                        borderColor:
                          selectedAccountOption === item
                            ? colors.primary
                            : colors.stroke,
                        borderRadius: 8,
                        marginBottom: 16,
                        justifyContent: "space-between",
                        flexDirection: "row",
                        alignItems: "center",
                        paddingLeft: 16,
                        paddingRight: 16,
                      }}
                    >
                      <P style={styles.label}>{item}</P>
                      {selectedAccountOption === item ? (
                        <SvgXml xml={svg.purple_check} />
                      ) : (
                        <View
                          style={{
                            width: 16,
                            height: 16,
                            borderRadius: 100,
                            borderWidth: 1,
                            borderColor: colors.stroke,
                          }}
                        ></View>
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
                <Input
                  label={selectedAccountOption}
                  placeholder="Enter account number"
                  inputStyle={{ width: "85%" }}
                  // contStyle={{ marginBottom: 16 }}
                  value={accountNumber}
                  error={!!accountNumberError}
                  onChangeText={handleAccountNumberChange}
                  keyboardType={
                    selectedAccountOption === "Account number"
                      ? "numeric"
                      : "default"
                  }
                />
                {accountNumberError ? (
                  <P style={styles.errorText}>{accountNumberError}</P>
                ) : null}

                <Input
                  label="Bank name"
                  placeholder="Enter bank name"
                  inputStyle={{ width: "85%" }}
                  contStyle={{ marginTop: 16 }}
                  value={bankName}
                  error={!!bankNameError}
                  onChangeText={handleBankNameChange}
                />
                {bankNameError ? (
                  <P style={styles.errorText}>{bankNameError}</P>
                ) : null}

                <Input
                  label="Account name"
                  placeholder="Enter account name"
                  inputStyle={{ width: "85%" }}
                  contStyle={{ marginTop: 16 }}
                  value={accountName}
                  error={!!accountNameError}
                  onChangeText={handleAccountNameChange}
                />
                {accountNameError ? (
                  <P style={styles.errorText}>{accountNameError}</P>
                ) : null}
              </View>
              <View style={{ width: "80%", marginTop: 32 }}>
                <Button btnText="Next" loading={ld} onPress={handleNext} />
              </View>
            </View>
          </ScrollView>
        </View>
        {/* {showQrCode && (
          <BarCodeScanner
            visible={showQrCode}
            onScan={handleScan}
            onClose={() => {
              setShowQrCode(false);
            }}
          />
        )} */}
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    // justifyContent:"center",
    alignItems: "center",
  },
  benefeciary: {
    paddingTop: 4,
    paddingRight: 16,
    paddingBottom: 4,
    paddingLeft: 16,
    backgroundColor: "#F7F4FF",
    borderRadius: 8,
    flexDirection: "row",
    width: "100%",
    minHeight: 44,
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
    marginTop: 16,
  },
  deatilsHead: {
    width: "100%",
    height: 42,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: "5%",
  },
  detailWrap: {
    padding: 24,
    width: "90%",
    alignSelf: "center",
    // height:200,
    backgroundColor: "white",
    borderRadius: 12,
    justifyContent: "center",
    // alignItems: "center",
  },
  detailWrap2: {
    // padding: 24,
    width: "90%",
    alignSelf: "center",
    minHeight: 246,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent: "center",
    alignItems: "center",
    marginTop: 60,
    paddingBottom: 20,
  },

  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  label: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
    textAlign: "left",
  },
});
