import React, { useState, useEffect, useRef } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  Keyboard,
  TextInput,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Link from "../../components/Link";

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");

export default function AccountVerification3({ navigation }) {
  const ref_input1 = useRef();
  const ref_input2 = useRef();
  const ref_input3 = useRef();
  const ref_input4 = useRef();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const refs = [ref_input1, ref_input2, ref_input3, ref_input4];
  const [fields, setFields] = useState(["", "", "", ""]);
  const [show, setShow] = useState(true);
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);

  const focusNextField = (nextField: any) => {
    nextField.current.focus();
  };

  const handleKeyPress = (index: any, event: any) => {
    const { key, nativeEvent } = event;

    if (nativeEvent.key === "Backspace" || nativeEvent.key === "Delete") {
      if (fields[index] === "") {
        const prevIndex = index - 1;
        if (prevIndex >= 0) {
          setFields((prevFields) => {
            const updatedFields = [...prevFields];
            updatedFields[prevIndex] = "";
            return updatedFields;
          });
          focusNextField(refs[prevIndex]);
        }
      } else {
        setFields((prevFields) => {
          const updatedFields = [...prevFields];
          updatedFields[index] = "";
          return updatedFields;
        });
      }
    }
  };

  const handleChangeText = (index: any, text: any) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;

      if (text !== "") {
        const nextIndex = index + 1;
        if (nextIndex < updatedFields.length) {
          focusNextField(refs[nextIndex]);
        } else {
          if (index === updatedFields.length - 1) {
            Keyboard.dismiss();
          }
        }
      }

      return updatedFields;
    });
  };

  const handlePaste = (index: any, pastedText: string) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      const characters = pastedText.split("");

      for (let i = 0; i < characters.length; i++) {
        const fieldIndex = index + i;
        if (fieldIndex < updatedFields.length) {
          updatedFields[fieldIndex] = characters[i];
        }
      }

      return updatedFields;
    });
  };
  const handleFocus = (index: number) => {
    setActiveIndex(index);
  };

  const handleBlur = () => {
    setActiveIndex(null);
  };

  const handleSubmit = () => {
    setIsSubmitted(true);
    if (fields.every((field) => field !== "")) {
      setLoading(true);
      navigation.navigate("ConfirmTransactionPin", { pin: fields.join("")});
      console.log(fields.join(""));
      setTimeout(() => {
        setLoading(false)
      }, 2000);
    } else {
   
    }
  };

  const getInputBorderStyle = (index) => {
    // Red border if field is empty and the form has been submitted
    return {
      borderColor: fields[index] === "" && isSubmitted ? colors.red : "#E6E5E5",
    };
  };

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Account verification"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentCard}>
            <View style={styles.section1Wrap}>
              <H4 style={styles.amt}>Transaction PIN</H4>
              <H4
                // @ts-ignore
                style={[
                  styles.amt,
                  {
                    fontSize: 12,
                    lineHeight: (19.2 / baseHeight) * height,
                    color: colors.gray,
                    textAlign: "center",
                    fontFamily: fonts.poppinsRegular,
                  },
                ]}
              >
                Enter a transaction PIN to secure your account and money
              </H4>

              <View style={styles.section2Wrap}></View>
              <View style={styles.section3Wrap}>
                <View style={styles.con}>
                  {refs.map((ref, index) => (
                    <View
                      style={[
                        styles.pinInput,
                        {
                          borderColor:
                            activeIndex === index
                              ? colors.primary
                              : getInputBorderStyle(index).borderColor,
                        },
                      ]}
                      key={index}
                    >
                      <TextInput
                        style={styles.pinTextInput}
                        placeholderTextColor="#000"
                        keyboardType="numeric"
                        ref={ref}
                        onChangeText={(text) => handleChangeText(index, text)}
                        onKeyPress={(event) => handleKeyPress(index, event)}
                        // @ts-ignore
                        onTextInput={(event) => {
                          const pastedText = event.nativeEvent.text;
                          handlePaste(index, pastedText);
                        }}
                        value={fields[index]}
                        secureTextEntry={show}
                        onFocus={() => handleFocus(index)}
                        onBlur={handleBlur}
                      />
                    </View>
                  ))}
                </View>
              </View>
            </View>
          </View>

          <P
            style={{
              alignSelf: "center",
              textAlign: "center",
              marginTop: (16 / baseHeight) * height,
              fontSize: 12,
              lineHeight: (18 / baseHeight) * height,
              width: "90%",
              color: colors.gray,
              fontFamily: fonts.poppinsRegular,
            }}
          >
            This process ensures that your account remains safe and secure.
          </P>
          <View style={styles.btnCont}>
            <Button
              btnText={"Continue"}
              onPress={handleSubmit}
              loading={loading}
            />
            {/* <Link
              style={{
                textAlign: "center",
                marginTop: (16 / baseHeight) * height,
              }}
              onPress={() => navigation.navigate("Home")}
            >
              Submit & return home
            </Link> */}
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    marginTop: (2.7 * height) / 100,
    paddingTop: (24 / baseHeight) * height,
    paddingBottom: (24 / baseHeight) * height,
    paddingLeft: (16 / baseWidth) * width,
    paddingRight: (16 / baseWidth) * width,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  amt: {
    fontSize: 16,
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: (24 / baseHeight) * height,
    borderTopWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  section3Wrap: {
    width: "100%",
    marginTop: (24 / baseHeight) * height,
    borderColor: colors.stroke,
  },
  con: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: (264 / baseWidth) * width,
    alignSelf: "center",
    marginBottom: 32,
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    marginHorizontal: 5 / baseWidth * width,
    width: 48 / baseWidth * width,
    height: 48 / baseWidth * width,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 18 / baseHeight * height,
    marginBottom: 18 / baseHeight * height,
  },
  pinTextInput: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
    width: (48 / baseWidth) * width,
    height: (48 / baseWidth) * width,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: (32 / baseHeight) * height,
    marginBottom: (64 / baseHeight) * height,
  },
});
