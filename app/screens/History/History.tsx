import React, { useCallback, useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  FlatList,
  ActivityIndicator,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import ListItem from "../../components/ListItem";
import BottomSheet from "../../components/BottomSheet";
import {
  GetTransation,
  GetTransationByFilters,
} from "../../RequestHandlers/Wallet";
import { useFocusEffect } from "@react-navigation/native";
import { formatDate, formatDate2 } from "../../components/FormatDate";
import { HistorySkeleton } from "../../Skeletons/Skeletons";
import Loader from "../../components/ActivityIndicator";
import DateOfBirthPicker from "../../components/DatePicker";
import NetInfo from "@react-native-community/netinfo";
import Offline from "../../components/ErrorSate/Offline";
import FailedToLoad from "../../components/ErrorSate/FailedToLoad";
import {
  getTransactionIcon,
  getTransactionLabel,
  TransactionClick,
} from "../../Utils/TransactionClick";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import { handleToast } from "../../components/Toast";

const { width, height } = Dimensions.get("window");

const groupByDate = (items) => {
  return items.reduce((acc, item) => {
    const dateOnly = new Date(item.updatedAt).toISOString().split("T")[0];
    acc[dateOnly] = acc[dateOnly] || [];
    acc[dateOnly].push(item);

    return acc;
  }, {});
};

const groupByCat = (items) => {
  return items.reduce((acc, item) => {
    acc[item.cat] = acc[item.cat] || [];
    acc[item.cat].push(item);
    return acc;
  }, {});
};

const baseHeight = 800;
const baseWidth = 360;
export default function HistoryScreen({ navigation }) {
  const [categories, setCategories] = useState(false);
  const [allStarts, setAllStarts] = useState(false);
  const [activeFilter, setActiveFilter] = useState("All categories");
  const [activeStatus, setActiveStatus] = useState("All status");
  const [showTranStatus, setShowTranStatus] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [transactions, setTransations] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [stat, setStat] = useState("all");
  const [cat, setCat] = useState("all");
  const [totalItem, setTotalItem] = useState(0);
  const [length, setLength] = useState(0);
  const [selectedDate, setSelectedDate] = useState("");
  const [fetchError, setfetchError] = useState(false);
  let onEndReachedCalledDuringMomentum = false;
  function capitalizeFirstLetter(word) {
    if (!word) return ""; // handle empty strings
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
  }
  const [isOnline, setIsOnline] = useState(false);

  const TIMEOUT_DURATION = 10000;
  const filters = [
    { name: "All categories", cat: "Quick action", filter: "all" },
    { name: "Add money", cat: "Quick action", filter: "add-money" },
    { name: "Send money", cat: "Quick action", filter: "send-money" },
    { name: "Card", cat: "Quick action", filter: "card" },
    { name: "QR code", cat: "Quick action", filter: "qr-code" },
    { name: "SFx point", cat: "Quick action", filter: "sfx-point" },
    // { name: "Airtime", cat: "Bill payment" },
    // { name: "Internet", cat: "Bill payment" },
    // { name: "Water", cat: "Bill payment" },
    // { name: "Electricity", cat: "Bill payment" },
  ];

  const status = [
    { name: "All status", filter: "all" },
    { name: "Successful", filter: "completed" },
    { name: "Pending", filter: "pending" },
    { name: "Failed", filter: "failed" },
  ];
  const getEmptyMessage = () => {
    if (cat === "add-money") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else if (cat === "send-money") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else if (cat === "card") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else if (cat === "sfx-point") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else if (stat === "completed") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else if (stat === "pending") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else if (stat === "failed") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else {
      // Default message for all categories and statuses
      return {
        title: "No history",
        description: "You have no transaction history yet.",
      };
    }
  };
  const emptyMessage = getEmptyMessage();
  const [loading1, setLoading1] = useState(false);
  const [limit, setLimit] = useState(40);
  const [hasMoreData, setHasMoreData] = useState(true);

  // Function to get transaction items
  const getTransaction = async (loadMore = false) => {
    if (loading) return; // Prevent fetching if already loading
    // setLoading(true);
    setLoading1(true);
    try {
      const transactions = await withApiErrorToast(GetTransationByFilters(
        1,
        limit,
        cat,
        stat,
        "",
        selectedDate
      ), handleToast); // Fetch using the current limit
      if (transactions) {
        setLoading(false);
        setLoading1(true);
        setTotalItem(transactions.meta.totalItems);
        setLength(transactions.items.length);
        if (transactions.items.length === 0) {
          setHasMoreData(false); // If no more transactions, stop loading
        } else {
          // Sort transactions
          const sortedTransactions = transactions.items.sort((a, b) => {
            return (
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            );
          });
          // Append new transactions if loading more, otherwise replace the list
          setTransations((prevTransactions) =>
            loadMore ? sortedTransactions : sortedTransactions
          );
        }
      }
      if (transactions.error) {
        setfetchError(true);
      } else {
        setfetchError(false);
      }
    } catch (error) {
      setLoading(false);
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Grouping transactions by date
  const groupedTransactions = groupByDate(transactions);
  const groupedFilters = groupByCat(filters);

  // Fetch transactions and update based on filters (stat)

  const checkConnection = () => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      if (state.isConnected === false) {
        setIsOnline(false);
      } else if (state.isConnected === true) {
        setIsOnline(true);
      }
    });
  };
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      if (state.isConnected === false) {
        setIsOnline(false);
      } else if (state.isConnected === true) {
        setIsOnline(true);
      }
    });
  }, []);
  useEffect(() => {
    setLoading(true);
  }, []);

  useFocusEffect(
    useCallback(() => {
      setLoading(true);
      setTransations([]);
      setLimit(20);
      getTransaction();
    }, [stat, cat, selectedDate])
  );

  // Function to load more data (increase the limit)
  const fetchMoreTransactions = () => {
    if (!loading && hasMoreData) {
      const newLimit = limit + 20;
      setLimit(newLimit);
      getTransaction(true);
    }
  };

  // Flatten the grouped transactions into a single array with headers
  const flattenedData = React.useMemo(() => {
    const result = [];
    const dateKeys = Object.keys(groupedTransactions).sort(
      (a, b) => new Date(b).getTime() - new Date(a).getTime()
    );

    dateKeys.forEach((date) => {
      // Add date header
      result.push({
        type: "header",
        date: date,
        id: `header-${date}`,
      });

      // Add transactions for this date
      groupedTransactions[date].forEach((transaction, index) => {
        result.push({
          type: "transaction",
          data: transaction,
          id: `transaction-${date}-${transaction.id || index}`,
        });
      });
    });

    return result;
  }, [groupedTransactions]);

  // Function to render each item (header or transaction)
  const renderItem = ({ item, index }) => {
    if (item.type === "header") {
      return (
        <View style={{ marginTop: index === 0 ? 16 : 24 }}>
          <P style={styles.datCat}>{formatDate2(item.date)}</P>
        </View>
      );
    }

    // Render transaction item
    const transaction = item.data;
    return (
      <TouchableOpacity
        onPress={() => {
          TransactionClick(transaction, navigation);
        }}
        style={{ marginTop: 8 }}
      >
        <View style={styles.item}>
          <SvgXml xml={getTransactionIcon(transaction, svg)} />
          <View style={{ marginLeft: 12 }}>
            <P style={styles.transactionAmount}>
              {getTransactionLabel(transaction)}
            </P>
            <P style={styles.transactionDate}>
              {formatDate(transaction?.updatedAt)}
            </P>
          </View>
          <View
            style={{
              position: "absolute",
              right: 16,
              top: 16,
              bottom: 16,
              alignItems: "flex-end",
              justifyContent: "center",
            }}
          >
            <P style={{ fontSize: 12, fontFamily: fonts.poppinsMedium }}>
              {`${
                transaction?.type === "DEPOSIT" ||
                transaction?.internalTransferSender
                  ? "+"
                  : "-"
              }$${
                transaction?.amount
                  ? transaction?.amount?.toFixed(2)?.toLocaleString()
                  : "..."
              }`}
              <P style={{ fontSize: 10 }}>
                {transaction.type === "sfxPoint" ? "SFxp" : ""}
              </P>
            </P>

            {transaction.type !== "sfxPoint" && (
              <P
                // @ts-ignore
                style={[
                  styles.transactionDate,
                  {
                    color: transaction.status
                      .toLowerCase()
                      .includes("completed")
                      ? colors.green
                      : transaction.status.toLowerCase().includes("pending") ||
                        transaction.status
                          .toLowerCase()
                          .includes("processing") ||
                        transaction.status.toLowerCase().includes("processing")
                      ? colors.yellow
                      : colors.red,
                  },
                ]}
              >
                {transaction?.status === "completed"
                  ? "Successful"
                  : transaction?.status === "processing"
                  ? "Pending"
                  : transaction?.status?.includes("awaiting-confirmation")
                  ? "Pending"
                  : capitalizeFirstLetter(transaction?.status)}
              </P>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <View style={styles.body}>
      <Div>
        {loading ? (
          <Loader />
        ) : (
          <>
            <AuthenticationHedear
              showBackArrow={false}
              text="History"
              navigation={navigation}
              iconComp={
                <TouchableOpacity
                  style={{ position: "absolute", right: 0 }}
                  onPress={() => {
                    if (selectedDate === "") {
                      setShowDatePicker(true);
                    } else {
                      setSelectedDate("");
                    }
                  }}
                >
                  <SvgXml
                    xml={selectedDate === "" ? svg.calander : svg.calander2}
                  />
                </TouchableOpacity>
              }
            />

            <View
              style={{
                width: "100%",
                borderTopWidth: 1,
                borderBottomWidth: 1,
                borderColor: colors.stroke,
                alignItems: "center",
              }}
            >
              <View style={{ width: "90%", height: 64, flexDirection: "row" }}>
                <TouchableOpacity
                  style={{
                    flexDirection: "row",
                    width: "50%",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  onPress={() => setCategories(true)}
                >
                  <P style={{ fontSize: 12, marginRight: 8 }}>{activeFilter}</P>
                  <SvgXml xml={svg.chevDown} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={{
                    flexDirection: "row",
                    width: "50%",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  onPress={() => setShowTranStatus(true)}
                >
                  <P style={{ fontSize: 12, marginRight: 8 }}>{activeStatus}</P>
                  <SvgXml xml={svg.chevDown} />
                </TouchableOpacity>
              </View>
            </View>

            {!isOnline ? (
              <Offline
                onPress={() => {
                  checkConnection();
                  getTransaction();
                }}
              />
            ) : fetchError ? (
              <FailedToLoad
                onPress={() => {
                  setLoading(true);
                  getTransaction();
                }}
              />
            ) : (
              <>
                <View style={styles.contentBody}>
                  {Object.keys(groupedTransactions).length === 0 ? (
                    <View style={styles.emptyCont}>
                      <SvgXml xml={svg.noTransaction} />
                      <P
                        style={{
                          fontFamily: fonts.poppinsMedium,
                          lineHeight: 21,
                          marginTop: 16,
                        }}
                      >
                        {emptyMessage.title}
                      </P>
                      <P
                        style={{
                          fontSize: 13,
                          fontFamily: fonts.poppinsRegular,
                          color: colors.gray2,
                          paddingLeft: 20,
                          paddingRight: 20,
                          textAlign: "center",
                        }}
                      >
                        {emptyMessage.description}
                      </P>
                    </View>
                  ) : (
                    <View
                      style={{
                        width: "90%",
                        alignSelf: "center",
                      }}
                    >
                      <FlatList
                        data={flattenedData}
                        renderItem={renderItem}
                        keyExtractor={(item) => item.id}
                        showsVerticalScrollIndicator={false}
                        onEndReached={() => {
                          if (!onEndReachedCalledDuringMomentum) {
                            fetchMoreTransactions();
                            onEndReachedCalledDuringMomentum = true;
                          }
                        }}
                        onMomentumScrollBegin={() => {
                          onEndReachedCalledDuringMomentum = false;
                        }}
                        onEndReachedThreshold={0.3}
                        contentContainerStyle={{
                          paddingTop: 16,
                          paddingBottom: 300,
                        }}
                        removeClippedSubviews={true}
                        maxToRenderPerBatch={10}
                        windowSize={10}
                        initialNumToRender={15}
                        ListFooterComponent={
                          loading1 && length < totalItem ? (
                            <ActivityIndicator
                              color={colors.primary}
                              style={{ marginTop: 16 }}
                            />
                          ) : null
                        }
                      />
                    </View>
                  )}
                </View>
              </>
            )}
          </>
        )}
      </Div>
      {/* {loading && <Loader />} */}
      <BottomSheet
        isVisible={categories}
        onClose={() => setCategories(false)}
        showBackArrow={false}
        backspaceText="Transaction type"
        modalContentStyle={{ height: "45%" }}
        extraModalStyle={{ height: "43%" }}
        components={
          <ScrollView>
            <View style={{ width: "100%", alignSelf: "center" }}>
              {Object.keys(groupedFilters).map((item) => (
                <React.Fragment key={`filter-group-${item}`}>
                  <P
                    style={{
                      marginTop: (24 / baseHeight) * height,
                      fontSize: 12,
                    }}
                    key={`filter-category-${item}`}
                  >
                    {item}
                  </P>
                  <View style={{ flexWrap: "wrap", flexDirection: "row" }}>
                    {groupedFilters[item].map((filterItem: any) => (
                      <TouchableOpacity
                        onPress={() => {
                          setActiveFilter(filterItem.name);
                          setCat(filterItem.filter);
                          setCategories(false);
                        }}
                        key={`filter-item-${filterItem.name}`}
                        style={{
                          paddingLeft: 16,
                          paddingRight: 16,
                          paddingTop: (13 / baseHeight) * height,
                          paddingBottom: (13 / baseHeight) * height,
                          backgroundColor:
                            activeFilter === filterItem.name
                              ? colors.primary
                              : colors.lowOpPrimary3,
                          marginRight: 16,
                          marginTop: (16 / baseHeight) * height,
                          borderRadius: 8,
                        }}
                      >
                        <P
                          style={{
                            fontSize: 12,
                            lineHeight: 18,
                            color:
                              activeFilter === filterItem.name
                                ? colors.white
                                : colors.black,
                          }}
                        >
                          {filterItem.name}
                        </P>
                      </TouchableOpacity>
                    ))}
                  </View>
                </React.Fragment>
              ))}
            </View>
          </ScrollView>
        }
      />
      <BottomSheet
        isVisible={showTranStatus}
        onClose={() => setShowTranStatus(false)}
        components={
          <ScrollView>
            <View>
              {status.map((item) => (
                <TouchableOpacity
                  key={`status-${item.name}`}
                  onPress={() => {
                    setActiveStatus(item.name);
                    setStat(item.filter);
                    setShowTranStatus(false);
                  }}
                  style={{
                    paddingTop: (13 / baseHeight) * height,
                    paddingBottom: (13 / baseHeight) * height,
                    paddingLeft: (16 / baseWidth) * width,
                    paddingRight: (16 / baseWidth) * width,
                    marginTop: 24,
                    borderRadius: 10,
                    backgroundColor:
                      activeStatus === item.name
                        ? colors.lowOpPrimary3
                        : "transparent",
                  }}
                >
                  <P style={{ fontSize: 12 }}>{item.name}</P>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        }
      />
      <BottomSheet
        isVisible={showDatePicker}
        backspaceText="Date"
        onClose={() => setShowDatePicker(false)}
        showBackArrow={false}
        components={
          <DateOfBirthPicker
            dateofbirth={false}
            selectedDate={selectedDate}
            onDateChange={(date) => {
              setSelectedDate(date);
            }}
            closeModal={() => {
              setShowDatePicker(false);
            }}
          />
        }
        modalContentStyle={{ height: "65%" }}
        extraModalStyle={{ height: "63%" }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.secBackground,
  },
  btnCard: {
    width: "90%",
    minHeight: 156,
    backgroundColor: "white",
    alignSelf: "center",
    marginTop: 24,
    borderRadius: 12,
    paddingTop: 16,
    paddingBottom: 16,
    // paddingLeft: 16,
    // paddingRight: 16,
  },
  btnSec1: {
    width: "100%",
    justifyContent: "space-around",
    flexDirection: "row",
    marginBottom: 24,
    // paddingHorizontal: 18.33
  },
  emptyCont: {
    width: "100%",
    height: "100%",
    alignItems: "center",
    // justifyContent: "center",
    paddingTop: (20 * height) / 100,
  },
  datCat: {
    fontSize: 12,
  },
  item: {
    width: "100%",
    padding: 16,
    backgroundColor: colors.white,
    marginTop: 8,
    borderRadius: 12,
    flexDirection: "row",
  },
  transactionAmount: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    // fontWeight: "bold",
  },
  transactionDate: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
});
