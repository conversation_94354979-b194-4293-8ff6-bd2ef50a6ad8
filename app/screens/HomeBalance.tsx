import React, { use<PERSON>allback, useContext, useEffect, useState } from "react";
import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  Text,
} from "react-native";
import { colors } from "../config/colors";
import P from "../components/P";
import { SvgXml } from "react-native-svg";
import { fonts } from "../config/Fonts";
import { svg } from "../config/Svg";
import BottomSheet from "../components/BottomSheet";
import CurrencySelect from "../components/CurrencySelect";
import { GetUserWallet } from "../RequestHandlers/Wallet";
import { useFocusEffect } from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { countries } from "../components/counties";
import Link from "../components/Link";
import Ionicons from "@expo/vector-icons/Ionicons";
import { CheckSession } from "../RequestHandlers/User";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";
import { useToast } from "../context/ToastContext";
import { formatToTwoDecimals } from "../Utils/numberFormat";
import { withApiErrorToast } from "../Utils/withApiErrorToast";

interface PProps {
  onScrollBeginDrag?: () => void;
  onScrollEndDrag?: () => void;
  onTouchStart?: () => void;
  onTouchEnd?: () => void;
  refreshing?: boolean;
  onRefresh?: () => void;
}
export default function HomeBalance({
  onScrollBeginDrag,
  onScrollEndDrag,
  onTouchStart,
  onTouchEnd,
  refreshing,
  onRefresh,
}: PProps) {
  const { handleToast } = useToast();
  const [showCountries, setShowCountries] = useState(false);
  const [country, setCountry] = useState("");
  const [balLoad, setBalLoad] = useState(false);
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  const [hideBal, setHideBal] = useState(false);
  const [amount, setAmount] = useState(0);
  const [trAmount, setTrAmount] = useState(0);
  const [flag, setFlag] = useState(require("../assets/turkey.png"));
  const [curSymbol, setCurSymbol] = useState("₺");
  const [curCode, setCurCode] = useState("TRY");
  const [localAmount, setLocalAmount] = useState(0);
  const [homeCountry, setHomeCountry] = useState("");
  const [id, setId] = useState("");
  const [wallets, setWallets] = useState([]);
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const handleActiveCountry = (newActiveType: string | null) => {
    setCountry(newActiveType);
  };
  const handleActiveHomeCountry = (newActiveType: string | null) => {
    if (newActiveType === "DR_Congo") {
      setHomeCountry("DR Congo");
    } else {
      setHomeCountry(newActiveType);
    }
    if (newActiveType !== "") {
      storeHomeCountry(newActiveType);
    }
  };
  const handleActiveFlag = (newActiveType: any | null) => {
    if (newActiveType) {
      setFlag(newActiveType);
    }
  };
  const handleActiveCurSymbol = (newActiveType: any | null) => {
    if (newActiveType) {
      setCurSymbol(newActiveType);
    }
  };
  const handleActiveCurCode = (newActiveType: any | null) => {
    if (newActiveType) {
      setCurCode(newActiveType);
    }
  };
  const getUserWallet = async (homeCountry: string, showLoader = false) => {
    // Only show loader if explicitly requested (refresh button, pull-to-refresh, or first load)
    if (showLoader) {
      setBalLoad(true);
    }
    try {
      const userWallet = await withApiErrorToast(GetUserWallet(homeCountry), handleToast);
      if (userWallet && userWallet.wallets && userWallet.wallets.length > 0) {
        setId(userWallet.wallets[0].user);
        setAmount(userWallet?.totalInUsd);
        setWallets(userWallet?.wallets);
        setTrAmount(userWallet?.totalInTRY);
        setLocalAmount(userWallet?.totalInLocal);

        // After successful first load, update the flag
        if (isFirstLoad) {
          setIsFirstLoad(false);
        }
      } else {
      }
    } catch (error) {
    } finally {
      setBalLoad(false);
      // Parent component will handle refreshing state
    }
  };
  // Fetch balance when homeCountry changes
  useEffect(() => {
    // Show loader only on first load
    getUserWallet(homeCountry, isFirstLoad);
  }, [homeCountry, isFirstLoad]);

  // Fetch balance whenever the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // Don't show loader when returning to the screen
      getUserWallet(homeCountry, false);
      return () => {};
    }, [homeCountry])
  );

  // Effect to handle parent's refresh action
  useEffect(() => {
    if (refreshing && onRefresh) {
      getUserWallet(homeCountry, true);
    }
  }, [refreshing, onRefresh, homeCountry]);

  // We now use the onRefresh prop from the parent component

  const storeHomeCountry = async (homeCountry: string) => {
    AsyncStorage.setItem(`currency${id}`, homeCountry).then(() => {});
  };

  const getActiveCurrency = async (id: string) => {
    try {
      // 1. Get stored home country
      const storedHomeCountry = await AsyncStorage.getItem(`currency${id}`);
      if (!storedHomeCountry) {
        return null;
      }
      // 2. Normalize country name for comparison
      const normalizedStoredCountry = storedHomeCountry.replace(/_/g, " ");
      // 3. Find matching country object
      const matchedCountry = countries.find(
        (country) =>
          country.country.toLowerCase() ===
          normalizedStoredCountry.toLowerCase()
      );

      // 4. Return results
      if (matchedCountry) {
        setHomeCountry(matchedCountry.country);
        setFlag(matchedCountry.flag);
        setCurCode(matchedCountry.currencyCode);
        setCurSymbol(matchedCountry.symbol);
        setCountry(matchedCountry.currency);
        getUserWallet(matchedCountry.country);
        //   return matchedCountry;
      }
    } catch (error) {
      return null;
    }
  };
  const clearLogin = async () => {
    await GoogleSignin.signOut();
    AsyncStorage.removeItem("cookies")
      .then(() => {
        // @ts-ignore
        setStoredCredentails(null);
      })
      .catch(() => {});
  };

  const checkSession = async () => {
    try {
      const res = await CheckSession();
      if (res.status == false || res.error) {
        clearLogin();
      }
    } catch (error) {
      console.error("Error checking session:", error);
      // Don't show a toast here as it might be confusing to users
    }
  };
  useEffect(() => {
    checkSession();
  }, []);
  useEffect(() => {
    getActiveCurrency(id);
  }, [id]);

  return (
    <>
      <View
        style={{
          width: "100%",
          alignSelf: "center",
          alignItems: "center",
        }}
      >
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            width: "90%",
            justifyContent: "space-between",
          }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <P style={{ fontSize: 13, color: colors.white }}>Total balance</P>
            <TouchableOpacity
              style={{
                marginLeft: 8,
                width: 20,
                height: 20,
                justifyContent: "center",
              }}
              onPress={() => setHideBal((prevState) => !prevState)}
            >
              <SvgXml
                xml={hideBal === true ? svg.eyeCloseWhite : svg.eyeOpenWhite}
              />
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            onPress={() => {
              // Show loader when manually refreshing
              getUserWallet(homeCountry, true);
            }}
            style={{
              flexDirection: "row",
              alignItems: "center",
              gap: 8,
            }}
          >
            <P
              style={{
                color: colors.white,
                fontSize: 12,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              Refresh balance
            </P>
            <Ionicons name="refresh" size={14} color="white" />
          </TouchableOpacity>
        </View>
        <ScrollView
          horizontal
          style={{ marginTop: 12, marginBottom: 24 }}
          showsHorizontalScrollIndicator={false}
          onScrollBeginDrag={onScrollBeginDrag}
          onScrollEndDrag={onScrollEndDrag}
          onTouchStart={onTouchStart}
          onTouchEnd={onTouchEnd}
          contentContainerStyle={{
            gap: 16,
            paddingLeft: 24,
            paddingRight: 24,
          }}
        >
          <View style={styles.newBalCard}>
            <Image
              source={require("../assets/usa.png")}
              style={{ width: 24, height: 24 }}
            />
            <P style={styles.hText}>United state dollar</P>
            {balLoad ? (
              <View style={{ width: "100%", alignItems: "flex-start" }}>
                <ActivityIndicator
                  color={colors.primary}
                  style={{ marginTop: 16 }}
                />
              </View>
            ) : (
              <P
                style={{
                  fontSize: 20,
                  lineHeight: 36,
                }}
              >
                {hideBal
                  ? "******"
                  : `$${formatToTwoDecimals(amount)}`}
                <Text
                  style={{
                    fontSize: 14,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  {hideBal ? "***" : "USD"}
                </Text>
              </P>
            )}
          </View>
          <View style={styles.newBalCard}>
            <Image
              source={require("../assets/turkey.png")}
              style={{ width: 24, height: 24 }}
            />
            <P style={styles.hText}>Turkish lira</P>
            {balLoad ? (
              <View style={{ width: "100%", alignItems: "flex-start" }}>
                <ActivityIndicator
                  color={colors.primary}
                  style={{ marginTop: 16 }}
                />
              </View>
            ) : (
              <P
                style={{
                  fontSize: 20,
                  lineHeight: 36,
                  color: colors.black,
                }}
              >
                {hideBal
                  ? "******"
                  : `₺${formatToTwoDecimals(trAmount)}`}
                <Text
                  style={{
                    fontSize: 14,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  {hideBal ? "***" : "TRY"}
                </Text>
              </P>
            )}
          </View>
          <TouchableOpacity
            style={styles.newBalCard}
            onPress={() => {
              setShowCountries(true);
            }}
          >
            {homeCountry === "" ? (
              <>
                <View style={styles.emptyCur}>
                  <SvgXml xml={svg.pPlus} />
                  <P
                    style={{
                      color: colors.primary,
                      textDecorationLine: "underline",
                    }}
                  >
                    Add currency
                  </P>
                </View>
              </>
            ) : (
              <>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <Image source={flag} style={styles.imgSy} />
                  <SvgXml xml={svg.arrowDown} style={{ marginLeft: 6 }} />
                </View>
                <P style={styles.hText}>{country}</P>
                {balLoad ? (
                  <View style={{ width: "100%", alignItems: "flex-start" }}>
                    <ActivityIndicator
                      color={colors.primary}
                      style={{ marginTop: 16 }}
                    />
                  </View>
                ) : (
                  <P
                    style={{
                      fontSize: 20,
                      lineHeight: 36,
                    }}
                    numberOfLines={1}
                  >
                    {hideBal
                      ? "******"
                      : `${curSymbol}${formatToTwoDecimals(localAmount)}`}
                    <Text
                      style={{
                        fontSize: 14,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      {hideBal ? "***" : curCode}
                    </Text>
                  </P>
                )}
              </>
            )}
          </TouchableOpacity>
        </ScrollView>
      </View>
      {/* select country */}
      <BottomSheet
        isVisible={showCountries}
        showBackArrow={false}
        backspaceText="Select country"
        onClose={() => setShowCountries(false)}
        modalContentStyle={{ height: "65%" }}
        extraModalStyle={{ height: "63%" }}
        components={
          <CurrencySelect
            onActiveCountryChange={handleActiveCountry}
            onActiveFlag={handleActiveFlag}
            onCurSymbolChange={handleActiveCurSymbol}
            onCurCodeChange={handleActiveCurCode}
            onActiveHomeCountry={handleActiveHomeCountry}
            initialSelectedCountry={country}
            initialHomeCountry={homeCountry}
            initialSymbol={curSymbol}
            initialCode={curCode}
            initialFlag={flag}
            onPress={() => {
              setShowCountries(false);
            }}
            excludedCountries={["Turkey"]}
          />
        }
      />
    </>
  );
}

const styles = StyleSheet.create({
  amount: {
    fontSize: 12,
  },
  newBalCard: {
    width: 220,
    height: 124,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
  },
  hText: {
    fontSize: 12,
    color: colors.dGray,
    marginTop: 16,
    fontFamily: fonts.poppinsRegular,
  },
  emptyCur: {
    width: "100%",
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 8,
  },
  imgSy: {
    width: 24,
    height: 24,
    borderRadius: 100,
  },
});
