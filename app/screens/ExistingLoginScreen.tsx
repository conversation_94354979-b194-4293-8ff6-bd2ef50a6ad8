import React, { useEffect, useState, useContext, useRef } from "react";
import {
  Dimensions,
  ScrollView,
  StyleSheet,
  View,
  Keyboard,
  Image,
  Platform,
  TextInput,
} from "react-native";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import Div from "../components/Div";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import H4 from "../components/H4";
import P from "../components/P";
import Input from "../components/Input";
import Link from "../components/Link";
import Button from "../components/Button";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";
import BottomComponent from "../components/BottomComponent";
import i18n from "../../i18n";
import { LanguageContext } from "../context/LanguageContext";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import {
  CheckSession,
  GetUserDetails,
  ValidatePin,
} from "../RequestHandlers/User";
import * as LocalAuthentication from "expo-local-authentication";
import { FingerPrintStatus } from "../context/FingerPrintContext";
import EnableBiomatricComponent from "../components/EnableBiomatricComponent";
import { TransactionAuth } from "../components/TransactionAuth";
import { encryptPIN } from "../Utils/encrypt";
import { useToast } from "../context/ToastContext";
import { withApiErrorToast } from "../Utils/withApiErrorToast";

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function ExistingLoginScreen({ navigation }) {
  const { handleToast } = useToast();
  // @ts-ignore
  const { language, changeLanguage } = useContext(LanguageContext);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [firstName, setFirstName] = useState("...");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [profile, setProfile] = useState(
    require("../assets/defualtAvatar.png")
  );
  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState(false);
  const [isBioMatricSupported, setIsBioMatricSupported] = useState(true);
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const {
    storedFingerPrintStatus,
    setStoredFingerPrintStatus,
    storedPrivateKey,
  } = useContext(FingerPrintStatus);
  const ref_input1 = useRef();
  const ref_input2 = useRef();
  const ref_input3 = useRef();
  const ref_input4 = useRef();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const refs = [ref_input1, ref_input2, ref_input3, ref_input4];
  const [fields, setFields] = useState(["", "", "", ""]);
  const [show, setShow] = useState(true);
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [showEnabler, setShowEnabler] = useState(false);
  const [id, setId] = useState("");
  const [is2faEnabled, setIs2faEnabled] = useState(false);

  const focusNextField = (nextField: any) => {
    nextField.current.focus();
  };

  const handleKeyPress = (index: any, event: any) => {
    const { key, nativeEvent } = event;
    if (nativeEvent.key === "Backspace" || nativeEvent.key === "Delete") {
      if (fields[index] === "") {
        const prevIndex = index - 1;
        if (prevIndex >= 0) {
          setFields((prevFields) => {
            const updatedFields = [...prevFields];
            updatedFields[prevIndex] = "";
            return updatedFields;
          });
          focusNextField(refs[prevIndex]);
        }
      } else {
        setFields((prevFields) => {
          const updatedFields = [...prevFields];
          updatedFields[index] = "";
          return updatedFields;
        });
      }
    }
  };
  const handleChangeText = (index: any, text: any) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;
      if (text !== "") {
        const nextIndex = index + 1;
        if (nextIndex < updatedFields.length) {
          focusNextField(refs[nextIndex]);
        } else {
          if (index === updatedFields.length - 1) {
            Keyboard.dismiss();
          }
        }
      }
      return updatedFields;
    });
  };
  const handlePaste = (index: any, pastedText: string) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      const characters = pastedText.split("");
      for (let i = 0; i < characters.length; i++) {
        const fieldIndex = index + i;
        if (fieldIndex < updatedFields.length) {
          updatedFields[fieldIndex] = characters[i];
        }
      }

      return updatedFields;
    });
  };
  const handleFocus = (index: number) => {
    setActiveIndex(index);
  };

  const handleBlur = () => {
    setActiveIndex(null);
  };
  const func = () => {
    navigation.reset({
      index: 0,
      routes: [
        {
          name: "BottomTabNavigator",
        },
      ],
    });
  };
  const navigate = async () => {
    const res = await AsyncStorage.getItem("login2fa");
    if (is2faEnabled && !res) {
      navigation.navigate("TwofactorAuthScreen2", {
        type: "login",
        ActivityFunction: func,
      });
    } else {
      // Check if this is the first time the user has logged in
      const checkFirstTimeLogin = async () => {
        try {
          const hasLoggedInBefore = await AsyncStorage.getItem(
            "hasLoggedInBefore"
          );
          // Navigate to the home screen
          navigation.reset({
            index: 0,
            routes: [
              {
                name: "BottomTabNavigator",
                params: { newUser: !hasLoggedInBefore },
              },
            ],
          });

          // If this is the first time logging in, mark it for future reference
          if (!hasLoggedInBefore) {
            await AsyncStorage.setItem("hasLoggedInBefore", "true");
          }
        } catch (error) {
          console.error("Error checking login status:", error);
          // Navigate anyway in case of error
          navigation.reset({
            index: 0,
            routes: [{ name: "BottomTabNavigator" }],
          });
        }
      };

      checkFirstTimeLogin();
    }
  };
  const ValidatePIN = async (pin, type: string) => {
    try {
      const body = {
        pin: type === "biomatric" ? pin : await encryptPIN(String(pin)),
        activityType: "login-pin",
      };
      const validate = await withApiErrorToast(ValidatePin(body), handleToast);
      if (validate.status === true) {
        setLoading(false);
        if (type !== "biomatric") {
          // handleToast(validate.message);
        }
        navigate();
      } else {
        setLoading(false);
        setShowEnabler(false);
        if (type === "biomatric") {
          handleToast("An error occurred. Please try again.", "error");
        } else {
          handleToast(validate.message, "error");
        }
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const handleSubmit = async () => {
    setIsSubmitted(true);
    if (fields.every((field) => field !== "")) {
      setLoading(true);
      ValidatePIN(fields.join(""), "pin");
    } else {
    }
  };
  const handleBiomatricSubmit = async () => {
    if (storedPrivateKey) {
      setLoading(true);
      ValidatePIN(storedPrivateKey, "biomatric");
    }
  };
  function ensureHttps(url) {
    if (url?.startsWith("http://")) {
      return url?.replace("http://", "https://");
    }
    return url;
  }
  const getInputBorderStyle = (index) => {
    // Red border if field is empty and the form has been submitted
    return {
      borderColor: fields[index] === "" && isSubmitted ? colors.red : "#E6E5E5",
    };
  };
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );
    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);
  const clearLogin = async () => {
    setLoader(true);
    await GoogleSignin.signOut();
    AsyncStorage.removeItem("login2fa")
      .then(() => {})
      .catch((error) => {});
    AsyncStorage.removeItem("cookies")
      .then(() => {
        setLoader(false);
        // @ts-ignore
        setStoredCredentails(null);
      })
      .catch((err) => {});
  };
  const getUserDetails = async () => {
    try {
      const userDetails = await withApiErrorToast(
        GetUserDetails(),
        handleToast
      );
      if (userDetails.error) {
      } else {
        setFirstName(userDetails?.firstName);
        setLastName(userDetails?.lastName);
        setProfile({ uri: ensureHttps(userDetails?.picture) });
        setEmail(userDetails?.email);
        setId(userDetails?.id);
        setIs2faEnabled(userDetails?._2faEnabled);
      }
    } catch (error) {}
  };
  const persistBiomatric = () => {
    AsyncStorage.setItem(`fingerPrintStatus${id}`, "true")
      .then(() => {
        // @ts-ignore
        setStoredFingerPrintStatus("true");
        handleBiomatricSubmit();
      })
      .catch((err) => {});
  };
  useEffect(() => {
    getUserDetails();
    async () => {
      const isCompatible = await LocalAuthentication.hasHardwareAsync();
      if (!isCompatible) {
        setIsBioMatricSupported(false);
        throw new Error("Your device isn't compatible.");
      }
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      if (!isEnrolled) {
        setIsBioMatricSupported(false);
        throw new Error("No Faces / Fingers found.");
      }
    };
  }, []);

  const checkSession = async () => {
    try {
      const res = await CheckSession();
      if (res.status == false || res.error) {
        clearLogin();
      }
    } catch (error) {}
  };

  useEffect(() => {
    checkSession();
  }, []);

  return (
    <View style={styles.container}>
      <Div>
        <ScrollView
          contentContainerStyle={{ paddingBottom: "10%" }}
          automaticallyAdjustKeyboardInsets={true}
        >
          <View>
            <View style={styles.lgCont}>
              <Image
                source={profile}
                style={{
                  width: 80,
                  height: 80,
                  objectFit: "cover",
                  marginBottom: 2,
                  borderRadius: 100,
                }}
              />
              <H4 style={{ fontFamily: fonts.poppinsSemibold }}>
                Welcome, {lastName}
              </H4>
              <P
                style={{
                  fontSize: 14,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Enter your login PIN below
              </P>
            </View>
            <View style={styles.form}>
              <View style={styles.section3Wrap}>
                <View style={styles.con}>
                  {refs.map((ref, index) => (
                    <View
                      style={[
                        styles.pinInput,
                        {
                          borderColor:
                            activeIndex === index
                              ? colors.primary
                              : getInputBorderStyle(index).borderColor,
                        },
                      ]}
                      key={index}
                    >
                      <TextInput
                        style={styles.pinTextInput}
                        placeholderTextColor="#000"
                        keyboardType="numeric"
                        ref={ref}
                        onChangeText={(text) => handleChangeText(index, text)}
                        onKeyPress={(event) => handleKeyPress(index, event)}
                        // @ts-ignore
                        onTextInput={(event) => {
                          const pastedText = event.nativeEvent.text.slice(0, 1);
                          handlePaste(index, pastedText);
                        }}
                        value={fields[index]}
                        secureTextEntry={show}
                        onFocus={() => handleFocus(index)}
                        onBlur={handleBlur}
                        maxLength={1}
                        // Add these additional props for extra protection
                        autoComplete="off"
                        autoCorrect={false}
                        autoCapitalize="none"
                      />
                    </View>
                  ))}
                </View>
                <Link
                  style={{
                    textDecorationLine: "underline",
                    fontSize: 12,
                    marginTop: 6,
                  }}
                  onPress={() => {
                    navigation.navigate("ExistingFp", {
                      email: email,
                      is2faEnabled: is2faEnabled,
                    });
                  }}
                >
                  {i18n.t("fp2")}?
                </Link>
              </View>
              <View style={styles.btnCont}>
                <Button
                  btnText={i18n.t("login.login")}
                  onPress={handleSubmit}
                  loading={loading}
                />
                {isBioMatricSupported && (
                  <View style={styles.seprator}>
                    <View style={styles.line}></View>
                    <P
                      style={{
                        fontSize: 12,
                        fontFamily: fonts.poppinsRegular,
                        color: colors.gray,
                      }}
                    >
                      {i18n.t("login.or")}
                    </P>
                    <View style={styles.line}></View>
                  </View>
                )}
                {isBioMatricSupported && (
                  // <></>
                  <Button
                    onPress={() => {
                      if (
                        storedFingerPrintStatus &&
                        storedPrivateKey !== null
                      ) {
                        TransactionAuth(handleBiomatricSubmit, handleToast);
                      } else {
                        setShowEnabler(true);
                      }
                    }}
                    style={{
                      marginTop: (16 / baseHeight) * height,
                      backgroundColor: colors.white,
                    }}
                    icon={
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        <SvgXml
                          xml={
                            Platform.OS === "ios"
                              ? svg.faceIdGray
                              : svg.fingerPrint
                          }
                          style={{ marginRight: 4 }}
                        />
                        <P style={{ fontSize: 12, color: colors.black }}>
                          {Platform.OS === "ios"
                            ? i18n.t("faceId")
                            : i18n.t("fingerprint")}
                        </P>
                      </View>
                    }
                    type="alt"
                  />
                )}
              </View>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginTop: (3 * height) / 100,
                  width: "100%",
                  justifyContent: "center",
                  // backgroundColor: "red",
                }}
              >
                <P
                  style={{
                    fontSize: 12,
                    fontFamily: fonts.poppinsRegular,
                    marginRight: 4,
                  }}
                >
                  {i18n.t("dha2")}
                </P>
                <Link
                  style={{ fontSize: 12, textDecorationLine: "underline" }}
                  onPress={() => {
                    clearLogin();
                  }}
                >
                  {" "}
                  {i18n.t("so")}
                </Link>
              </View>
            </View>
          </View>
        </ScrollView>
        {showEnabler && (
          <EnableBiomatricComponent
            visible={showEnabler}
            onClose={() => {
              setShowEnabler(false);
            }}
            secondaryFunction={() => {
              persistBiomatric();
            }}
          />
        )}
        <BottomComponent
          navigation={navigation}
          contStyle={{ backgroundColor: colors.white }}
        />

        {/* <Toast /> */}
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  lgCont: {
    width: "90%",
    alignSelf: "center",
    alignItems: "center",
    marginTop: (6 * height) / 100,
  },
  form: {
    width: (90 * width) / 100,
    minHeight: (50 * height) / 100,
    alignSelf: "center",
    marginTop: (2.4 * height) / 100,
  },
  inputWrap: {
    marginBottom: 16,
  },
  btnCont: {
    width: "100%",
    // height: (20 * height) / 100,
    // height: 139,
    // backgroundColor: "red",
    justifyContent: "space-between",
    marginTop: (5 * height) / 100,
  },
  bottomCont: {
    position: "absolute",
    bottom: 42,
    width: "90%",
    flexDirection: "row",
    backgroundColor: "white",
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "space-between",
  },
  bottomIcons: {
    width: 104,
    height: 22,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  bottomIconsText: {
    fontSize: 12,
    lineHeight: 22,
    color: "#A5A1A1",
    marginLeft: 4,
    textDecorationLine: "underline",
    textDecorationColor: "#A5A1A1",
  },
  line: {
    width: "45%",
    height: 1,
    backgroundColor: colors.stroke,
  },
  seprator: {
    width: "100%",
    lineHeight: 19,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: (16 / baseHeight) * height,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  section3Wrap: {
    width: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  con: {
    flexDirection: "row",
    width: "100%",
    // backgroundColor: "red",
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    // marginBottom: 32,
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    width: 56,
    height: 56,
    alignItems: "center",
    justifyContent: "center",
  },
  pinTextInput: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
    width: (48 / baseWidth) * width,
    height: (48 / baseWidth) * width,
  },
});
