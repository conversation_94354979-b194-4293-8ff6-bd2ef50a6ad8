import React, { useContext, useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
  Linking,
} from "react-native";
import { fonts } from "../../../config/Fonts";
import Div from "../../../components/Div";
import AuthenticationHedear from "../../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import P from "../../../components/P";
import MicroBtn from "../../../components/MicroBtn";
import { colors } from "../../../config/colors";
import DetailCard from "../../../components/DetailCard";
import Button from "../../../components/Button";
import SendMoneyStatus from "../../../components/SeendMoneyStatus";
import Link from "../../../components/Link";
import {
  GetRateByCountry,
  GetTransationById,
} from "../../../RequestHandlers/Wallet";
import * as Clipboard from "expo-clipboard";
import { formatDate } from "../../../components/FormatDate";
import Loader from "../../../components/ActivityIndicator";
import { TestBanks } from "../../../components/TestBanks";
import FailedToLoad from "../../../components/ErrorSate/FailedToLoad";
import { useToast } from "../../../context/ToastContext";
import { calculateWithdrawalFeeSync } from "../../../Utils/feeCalculations";
import { CredentailsContext } from "../../../RequestHandlers/CredentailsContext";
import { withApiErrorToast } from "../../../Utils/withApiErrorToast";
import { formatToTwoDecimals } from "../../../Utils/numberFormat";

const { width, height } = Dimensions.get("window");

export default function LinkSendTransactionDetails({ navigation, route }) {
  const [showSendStatus, setShowSendStatus] = useState(false);
  const { handleToast } = useToast();
  const [tranStat, setTranStat] = useState("successful");
  const { id } = route.params || "";
  const [resData, setResData] = useState<any>([]);
  const [loader, setLoader] = useState(false);
  const [yellowCardCode, setyellowCardCode] = useState("");
  const [curDetails, setCurDetails] = useState<any>([]);
  const [yData, setYData] = useState<any>([]);
  const [symbol, setSymbol] = useState("₦");
  const [bankImg, setBankImg] = useState(
    "https://res.cloudinary.com/dqw0lwkil/image/upload/v1675739938/LINK/Bank_List/palmpaybank_ayyotp.png"
  );
  const [isDataLaodFailed, setIsDataLoadFailed] = useState(false);
  const { storedCredentails } = useContext(CredentailsContext);

  const copyAccNum = async (accNum) => {
    const copiedText = await Clipboard.setStringAsync(accNum);

    if (copiedText === true) {
      handleToast("Code copied to clipboard", "success");
    } else {
      handleToast("Error copying code", "error");
    }
  };
  function truncateNumber(number) {
    const numberString = number.toString();

    // If the number length is less than 6, return it as is
    if (numberString.length <= 6) {
      return numberString;
    }

    // Replace the middle digits with asterisks
    return numberString.slice(0, 4) + "****" + numberString.slice(-2);
  }

  const transaction = async () => {
    setLoader(true);
    try {
      const res = await withApiErrorToast(GetTransationById(id), handleToast);
      // if (res) {
      //   setLoader(false);
      //   setResData(res.transaction);
      // }
      if (res.error) {
        setIsDataLoadFailed(true);
      } else {
        setIsDataLoadFailed(false);
      }
      if (res.linkData) {
        setYData(res.linkData[0]);
        setResData(res.transaction);
      }
      TestBanks.BankList[0].banks.map((a, b) => {
        if (a.name.includes(res?.linkData[0]?.transactions?.bank_name)) {
          setBankImg(a.image);
        }
      });
      if (res.transaction.status === "completed") {
        setTranStat("Successful");
      } else if (res.transaction.status === "failed") {
        setTranStat("Failed");
      } else {
        setTranStat("Pending");
      }
    } catch (error) {
    } finally {
      setLoader(false);
    }
  };
  useEffect(() => {
    transaction();
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction details"
          navigation={navigation}
        />
        <ScrollView>
          {isDataLaodFailed ? (
            <>
              <FailedToLoad
                onPress={() => {
                  setLoader(true);
                  transaction();
                }}
              />
            </>
          ) : (
            <>
              <View style={styles.contentBody}>
                <View style={styles.detailWrap}>
                  <>
                    {/* <SvgXml
                  xml={svg.bank2}
                  width={32}
                  height={32}
                  style={{
                    top: -15,
                    position: "absolute",
                    zIndex: 10,
                    alignSelf: "center",
                  }}
                /> */}
                    <Image
                      source={{ uri: bankImg }}
                      style={{
                        width: 34,
                        height: 34,
                        objectFit: "cover",
                        borderRadius: 100,
                        top: -15,
                        position: "absolute",
                        zIndex: 10,
                        alignSelf: "center",
                      }}
                    />
                    <DetailCard
                      amount={
                        <>
                          <P
                            style={{
                              fontSize: 24,
                              lineHeight: 36,
                              marginRight: 2,
                            }}
                          >
                            ${formatToTwoDecimals(Number(resData?.amount))}
                          </P>
                          <P style={{ marginTop: 5 }}>USD</P>
                        </>
                      }
                      convertedAmount={
                        <>
                          <P
                            style={{
                              fontSize: 16,
                              lineHeight: 24,
                              marginRight: 2,
                            }}
                          >
                            {symbol}
                            {formatToTwoDecimals(Number(resData?.localAmount))}
                          </P>
                          <P
                            style={{
                              marginTop: 2,
                              fontSize: 12,
                              lineHeight: 18,
                            }}
                          >
                            NGN
                          </P>
                        </>
                      }
                      timer={
                        <View style={styles.indicatorCont}>
                          <View
                            style={[
                              styles.indicatorDot,
                              {
                                backgroundColor:
                                  tranStat == "Successful"
                                    ? colors.green
                                    : tranStat === "Failed"
                                    ? colors.red
                                    : colors.yellow,
                              },
                            ]}
                          ></View>
                          <P style={{ fontSize: 10, lineHeight: 16 }}>
                            {tranStat}
                          </P>
                        </View>
                      }
                      lineStyle={{ borderStyle: "dashed", marginTop: 24 }}
                      bottomComponent={
                        <View style={styles.desCont}>
                          <View
                            style={{
                              paddingBottom: 24,
                              borderBottomWidth: 1,
                              borderColor: colors.stroke,
                              borderStyle: "dashed",
                            }}
                          >
                            {/* <View style={styles.items}>
                          <P style={styles.holder}>Sender</P>
                          <P style={styles.value}>
                            {yData?.transactions?.bank_name} |{" "}
                            {yData?.transactions?.account_number
                              ? truncateNumber(
                                  yData?.transactions?.account_number
                                )
                              : "NAN"}
                          </P>
                        </View> */}
                            <View style={styles.items}>
                              <P style={styles.holder}>Account number</P>
                              <P style={styles.value}>
                                {yData?.transactions?.account_number}
                              </P>
                            </View>
                            <View style={styles.items}>
                              <P style={styles.holder}>Bank</P>
                              <P style={styles.value}>
                                {yData?.transactions?.bank_name}
                              </P>
                            </View>
                            <View style={styles.items}>
                              <P style={styles.holder}>Account name</P>
                              <P style={styles.value}>
                                {" "}
                                {yData?.transactions?.account_name}
                              </P>
                            </View>
                            <View style={styles.items}>
                              <P style={styles.holder}>Note</P>
                              <P style={styles.value}> {resData.reason}</P>
                            </View>
                            <View style={styles.items}>
                              <P style={styles.holder}>Reference number</P>
                              <View
                                style={{
                                  flexDirection: "row",
                                  width: "60%",
                                  //   backgroundColor: "red",
                                  justifyContent: "flex-end",
                                  alignItems: "center",
                                }}
                              >
                                <TouchableOpacity
                                  onPress={() => {
                                    copyAccNum(resData?.ref);
                                  }}
                                >
                                  <SvgXml
                                    xml={svg.lightCopy}
                                    style={{ marginRight: 10 }}
                                  />
                                </TouchableOpacity>
                                <P
                                  // @ts-ignore
                                  style={[
                                    // styles.value,
                                    {
                                      textAlign: "right",
                                      fontSize: 12,
                                      lineHeight: 18,
                                      color: colors.black,
                                    },
                                  ]}
                                >
                                  {resData?.ref}
                                </P>
                              </View>
                            </View>
                            <View style={styles.items}>
                              <P style={styles.holder}>Timestamp</P>
                              <P style={styles.value}>
                                {formatDate(resData?.updatedAt)}
                              </P>
                            </View>
                          </View>
                          <View style={{ paddingTop: 24 }}>
                            <View style={styles.items}>
                              <P style={styles.holder}>Fee</P>
                              <P style={styles.value}>
                                <P
                                  // @ts-ignore
                                  style={[
                                    styles.value,
                                    { fontFamily: fonts.poppinsRegular },
                                  ]}
                                >
                                  {(() => {
                                    const nairaAmount =
                                      Number(resData?.amount) *
                                        resData?.exchangeRate || 0;
                                    const feeDetails =
                                      calculateWithdrawalFeeSync(nairaAmount);
                                    return `${(
                                      feeDetails?.totalFee /
                                      resData?.exchangeRate
                                    )?.toFixed(2)} USD`;
                                  })()}
                                </P>
                              </P>
                            </View>
                            <View style={styles.items}>
                              <P style={styles.holder}>Amount received</P>
                              <P style={styles.value}>
                                <P
                                  // @ts-ignore
                                  style={[
                                    styles.value,
                                    { fontFamily: fonts.poppinsRegular },
                                  ]}
                                >
                                  {(() => {
                                    // const mainAmount = resData?.amount;
                                    const nairaAmount =
                                      Number(resData?.localAmount) || 0;
                                    return `${formatToTwoDecimals(
                                      nairaAmount
                                    )} NGN`;
                                  })()}
                                </P>
                              </P>
                            </View>
                            <View style={styles.items}>
                              <P style={styles.holder}>Exchange rate</P>
                              <P style={styles.value}>
                                {" "}
                                1 USD ~ {resData?.exchangeRate?.toFixed(0)} NGN
                              </P>
                            </View>
                            <View style={styles.items}>
                              <P style={styles.holder}>Payment method</P>
                              <P style={styles.value}>
                                {resData?.type === "DEPOSIT"
                                  ? "Add money"
                                  : "Send money"}
                              </P>
                            </View>
                            <View style={[styles.items]}>
                              <P style={styles.holder}>Type</P>
                              <P style={styles.value}>Bank transfer</P>
                            </View>
                            <View style={[styles.items, { marginBottom: 0 }]}>
                              <P style={styles.holder}>Account</P>
                              <P style={styles.value}>USDC</P>
                            </View>
                          </View>
                        </View>
                      }
                    />
                  </>

                  <View style={styles.buttonWrap}>
                    {tranStat?.toLowerCase() === "successful" && (
                      <Button
                        btnText="View receipt"
                        onPress={() => {
                          navigation.navigate("LinkSendReciept", {
                            data: resData,
                            data2: yData,
                          });
                        }}
                      />
                    )}
                    <View
                      style={{
                        alignItems: "center",
                        flexDirection: "row",
                        justifyContent: "center",
                        marginTop: 32,
                      }}
                    >
                      <SvgXml xml={svg.chat} style={{ marginRight: 4 }} />
                      <Link
                        style={{ fontSize: 12 }}
                        onPress={() => {
                          const username =
                            storedCredentails?.user?.username || "Not provided";
                          const email =
                            storedCredentails?.user?.email || "Not provided";
                          const message = `Hi, Support, I have an issue that requires resolving.\nMy Username is ${username} and My email is ${email}`;
                          const encodedMessage = encodeURIComponent(message);
                          Linking.openURL(
                            `https://wa.me/905338563416?text=${encodedMessage}`
                          );
                        }}
                      >
                        Report transaction
                      </Link>
                    </View>
                  </View>
                </View>
              </View>
            </>
          )}
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
    // backgroundColor: "red",
    width: "60%",
    textAlign: "right",
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  indicatorCont: {
    padding: 19.5,
    paddingTop: 4,
    paddingBottom: 4,
    backgroundColor: colors.secBackground,
    marginTop: 8,
    borderRadius: 99,
    flexDirection: "row",
    alignItems: "center",
  },
  indicatorDot: {
    width: 8,
    height: 8,
    borderRadius: 99,
    marginRight: 4,
  },
});
