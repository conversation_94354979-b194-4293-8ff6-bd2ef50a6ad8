import React, { useState, useEffect, useContext, useCallback } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Linking,
  BackHandler,
} from "react-native";
import { colors } from "../../../config/colors";
import Div from "../../../components/Div";
import AuthenticationHedear from "../../../components/AuthenticationHedear";
import P from "../../../components/P";
import H4 from "../../../components/H4";
import { fonts } from "../../../config/Fonts";
import Button from "../../../components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import * as Clipboard from "expo-clipboard";
import Link from "../../../components/Link";
import { useFocusEffect } from "@react-navigation/native";
import { GetTransationById } from "../../../RequestHandlers/Wallet";
import { setTextRange } from "typescript";
import { DepositContext } from "../../../context/DepositeContext";
import { countries } from "../../../components/counties";
import { useToast } from "../../../context/ToastContext";
import { CredentailsContext } from "../../../RequestHandlers/CredentailsContext";
import Loader from "../../../components/ActivityIndicator";
import { withApiErrorToast } from "../../../Utils/withApiErrorToast";
import { formatNumberWithCommas, formatToTwoDecimals } from "../../../Utils/numberFormat";

const { width, height } = Dimensions.get("window");
export default function LinkSentMoney({ navigation, route }) {
  const { transaction } = route?.params || {};
  const [transactionStatus, setTransactionStatus] = useState("pending");
  const { deposit } = useContext(DepositContext);
  const [isRefCopied, setIsRefCopied] = useState(false);
  const [refNUm, setRefNum] = useState("SF1122334455");
  // const [timeLeft, setTimeLeft] = useState(25 * 60);
  const [timeLeft1, setTimeLeft1] = useState(25 * 60); // 25 minutes in seconds
  const [amountInDoller, setAmountInDoller] = useState(0);
  const [amInLocal, setamInLocal] = useState(0);
  const [currency, setCurrency] = useState("NGN");
  const [symbol, setSymbol] = useState("₦");
  const { handleToast } = useToast()
  const { storedCredentails } = useContext(CredentailsContext);
  const [loader, setLoader] = useState(false);
  const formatNumber = (value) => {
    value = value?.toString();
    return value?.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };
  // @ts-ignore
  const copyRefNum = async () => {
    const copiedText = await Clipboard.setStringAsync(refNUm);

    if (copiedText === true) {
      setIsRefCopied(true);
      setTimeout(() => {
        setIsRefCopied(false);
      }, 4000);
    }
  };
  const [timeLeft, setTimeLeft] = useState(0);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setTimeLeft((prevTimeLeft) => {
        if (prevTimeLeft <= 1) {
          clearInterval(intervalId);
          return 0;
        }
        return prevTimeLeft - 1;
      });
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);
  useEffect(() => {
    const intervalId = setInterval(() => {
      setTimeLeft1((prevTimeLeft) => {
        if (prevTimeLeft <= 1) {
          clearInterval(intervalId);
          return 0;
        }
        return prevTimeLeft - 1;
      });
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);

  //   const calculateTimeLeft = (expiresAt: string | Date): number => {
  //     const expiryDate = new Date(expiresAt);
  //     const now = new Date();
  //     const diffInSeconds = Math.floor(
  //       (expiryDate.getTime() - now.getTime()) / 1000
  //     );

  //     return diffInSeconds > 0 ? diffInSeconds : 0;
  //   };

  //   useEffect(() => {
  //     if (deposit?.payment?.expiresAt) {
  //       const expiresAt = deposit.payment.expiresAt;
  //       setTimeLeft(calculateTimeLeft(expiresAt));
  //       const intervalId = setInterval(() => {
  //         setTimeLeft((prevTimeLeft) => {
  //           if (prevTimeLeft <= 1) {
  //             clearInterval(intervalId);
  //             return 0;
  //           }
  //           return prevTimeLeft - 1;
  //         });
  //       }, 1000);
  //       return () => clearInterval(intervalId);
  //     }
  //   }, [deposit?.payment?.expiresAt]);

  // useEffect(() => {
  //   const intervalId = setInterval(() => {
  //     setIsTranSocketHit(true);
  //   }, 3000);

  //   return () => clearInterval(intervalId);
  // }, []);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };
  useFocusEffect(useCallback(() => {
    const onBackPress = () => {
      // Reset navigation stack to avoid loading issues
      // @ts-ignore
      navigation.reset({
        index: 0,
        routes: [{ name: 'BottomTabNavigator' }],
      });
      return true;
    };
    // Disable iOS swipe back gesture
    navigation.setOptions({
      gestureEnabled: false
    });
    // Handle Android back button
    BackHandler.addEventListener("hardwareBackPress", onBackPress);

    return () => {
      BackHandler.removeEventListener("hardwareBackPress", onBackPress);
    };
  }, [navigation]))
  const getTransactionbyId = async () => {
    try {
      const transactionById = await withApiErrorToast( GetTransationById(transaction.id), handleToast)
      if (transactionById.error) {
        handleToast("Error checking transaction status", "error");
      } else {
        setRefNum(transactionById?.transaction?.ref);
        setTransactionStatus(transactionById?.transaction?.status?.toLowerCase());
        setAmountInDoller(transactionById?.transaction?.amount);
        setamInLocal(transactionById?.transaction?.localAmount);
        setCurrency(transactionById?.transaction?.fromCurrency);
      }
      //   const getSymbolByCurrency = (fromCurrency: string) => {
      //     const matchedCountry = countries.find(
      //       (country) => country.currencyCode === fromCurrency
      //     );
      //     return matchedCountry ? matchedCountry.symbol : null;
      //   };
      //   const fromCurrency = transactionById.fromCurrency;
      //   const symbol = getSymbolByCurrency(fromCurrency);
      //   setSymbol(symbol);
      //   if (transactionById.status === "pending") {
      //   }
    } catch (error) {
      handleToast("Error getting transaction status", "error");
      ;
    } finally {
      setLoader(false)
    }
  };
  useEffect(() => {
    const intervalId = setInterval(() => {
      getTransactionbyId();
    }, 2000);
    return () => clearInterval(intervalId);
  }, []);

  
  useEffect(() => {
    setLoader(true)
    getTransactionbyId();
  }, []);


  if (loader) {
    return <Loader />;
  }

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Money sent" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentCard}>
            <View style={styles.section1Wrap}>
              <P style={styles.addMoney}>You will receive</P>
              <H4 style={styles.amt}>
                ${formatToTwoDecimals(Number(amountInDoller)) || 0}
                <P style={styles.amtCur}>USD</P>
              </H4>
              {/* @ts-ignore */}
              <H4 style={[styles.amt, { fontSize: 16, lineHeight: 24 }]}>
                ₦{formatNumberWithCommas(Number(amInLocal)) || 0}{" "}
                <P
                  style={{
                    fontSize: 12,
                    lineHeight: 24,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  {currency}
                </P>
              </H4>
              <View
                style={[
                  styles.timer,
                  {
                    backgroundColor:
                      transactionStatus === "failed"
                        ? colors.lowOpFaild
                        : transactionStatus === "completed"
                          ? colors.lowOpSuccess
                          : colors.secBackground,
                  },
                ]}
              >
                {transactionStatus === "failed" ? (
                  // @ts-ignore
                  <P style={[styles.statusText, { color: colors.red }]}>
                    Failed
                  </P>
                ) : transactionStatus === "completed" ? (
                  // @ts-ignore
                  <P style={[styles.statusText, { color: colors.green }]}>
                    Successful
                  </P>
                ) : (
                  // @ts-ignore
                  <P style={[styles.statusText, { color: colors.yellow }]}>
                    Pending
                  </P>
                )}
              </View>
              <View style={styles.section2Wrap}>
                <View>
                  <P style={styles.holder}>Reference number</P>
                  <P style={styles.value}>{refNUm}</P>
                </View>
                <TouchableOpacity onPress={copyRefNum} style={styles.copyBtn}>
                  <View style={styles.copyBtn}>
                    <P style={styles.copyText}>
                      {isRefCopied ? "Copied" : "Copy"}
                    </P>

                    <SvgXml
                      xml={isRefCopied ? svg.circleSuccess : svg.copy}
                      style={{ width: 14, height: 14 }}
                    />
                  </View>
                </TouchableOpacity>
              </View>
              <View style={styles.section3Wrap}>
                <View style={styles.progressDesCont}>
                  <View style={{ alignItems: "center", marginRight: 12 }}>
                    <SvgXml xml={svg.circleSuccess} />
                    <View style={styles.bar1}></View>
                  </View>
                  <View>
                    <P style={styles.progTextHead}>Money sent</P>
                    <P style={styles.progTextBody}>
                      You’ve confirmed that money {"\n"}has been sent
                    </P>
                  </View>
                </View>

                <View style={styles.progressDesCont}>
                  <View style={{ alignItems: "center", marginRight: 12 }}>
                    <SvgXml
                      xml={
                        transactionStatus === "completed"
                          ? svg.circleSuccess
                          : transactionStatus === "failed"
                            ? svg.circleError
                            : svg.circleInprogress
                      }
                    />
                    <View
                      style={[
                        styles.bar2,
                        {
                          backgroundColor:
                            transactionStatus === "completed"
                              ? colors.green
                              : colors.lowOpPrimary2,
                        },
                      ]}
                    ></View>
                  </View>
                  <View>
                    <P style={styles.progTextHead}>Money processing</P>
                    <P style={styles.progTextBody}>
                      We have received your money
                    </P>
                  </View>
                </View>

                <View style={styles.progressDesCont}>
                  <View style={{ alignItems: "center", marginRight: 12 }}>
                    <SvgXml
                      xml={
                        transactionStatus === "completed"
                          ? svg.circleSuccess
                          : svg.circleNull
                      }
                    />
                  </View>
                  <View>
                    <P style={styles.progTextHead}>Money added</P>
                    <P style={styles.progTextBody}>
                      Dollars have been added to{"\n"}your money app
                    </P>
                  </View>
                </View>
              </View>
            </View>
          </View>

          <P
            style={{
              textAlign: "center",
              marginTop: 16,
              fontSize: 12,
              lineHeight: 18,
              color: colors.gray,
              fontFamily: fonts.poppinsRegular,
            }}
          >
            We’ll send you update on your money status
          </P>
          <View style={styles.btnCont}>
            <Button
              btnText={"Okay"}
              onPress={() => navigation.navigate("BottomTabNavigator")}
            />
            {/* <Link
              style={{ textAlign: "center", fontSize: 12, marginTop: 16 }}
              onPress={() => {
                navigation.navigate("BankTransactionDetails2");
              }}
            >
              {transactionStatus === 'failed' || transactionStatus === 'complete'
                ? "Transaction details"
                : "View details"}
            </Link> */}
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                width: "100%",
                justifyContent: "center",
                marginTop: (2.5 * height) / 100,
              }}
            >
              <P
                style={{
                  lineHeight: 22.4,
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Do you need help?{" "}
              </P>
              <Link
                style={{ textDecorationLine: "underline" }}
                onPress={() => {
                  const username = storedCredentails?.user?.username || "Not provided";
                  const email = storedCredentails?.user?.email || "Not provided";
                  const message = `Hi, Support, I have an issue that requires resolving.\nMy Username is ${username} and My email is ${email}`;
                  const encodedMessage = encodeURIComponent(message);
                  Linking.openURL(
                    `https://wa.me/905338563416?text=${encodedMessage}`
                  );
                }}
              >
                Chat SFx team
              </Link>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    // marginTop: 24,
    marginTop: (2.7 * height) / 100,
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 16,
    paddingRight: 16,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    marginBottom: 4,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  copyBtn: {
    paddingTop: 4,
    paddingBottom: 4,
    padding: 13,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  addMoney: {
    fontSize: 12,
    lineHeight: 19.2,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  amt: {
    fontSize: (4 * height) / 100,
    // fontSize: 32,
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  amtCur: {
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  timer: {
    paddingTop: 4,
    paddingBottom: 4,
    paddingLeft: 16,
    paddingRight: 16,
    borderRadius: 99,
    marginTop: 16,
  },
  statusText: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsRegular,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: (2.7 * height) / 100,
    paddingTop: (2.7 * height) / 100,
    borderTopWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  section3Wrap: {
    width: "100%",
    marginTop: (2.7 * height) / 100,
    borderColor: colors.stroke,
    paddingLeft: 16,
  },
  barCont: {
    // backgroundColor: "red",
    alignItems: "center",
  },
  bar1: {
    height: 46,
    width: 2,
    borderRadius: 2,
    backgroundColor: colors.green,
    marginTop: 4,
    marginBottom: 4,
  },
  bar2: {
    height: 28,
    width: 2,
    borderRadius: 2,
    marginTop: 4,
    marginBottom: 4,
  },
  progressDesCont: {
    flexDirection: "row",
  },
  progTextHead: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  progTextBody: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: (5 * height) / 100,
    // marginTop: 42,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
});
