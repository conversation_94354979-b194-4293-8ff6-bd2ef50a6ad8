import AsyncStorage from "@react-native-async-storage/async-storage";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./Request";

const request = new RequestHandler();

export function GetReferralOverview(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`user/referral/overview`, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function GetReferralRank(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`user/referral/rank`, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function GetReferals(page:number, limit: number): Promise<any> {
    return new Promise((resolve, reject) => {
      AsyncStorage.getItem("cookies")
        .then((res) => {
          let infomation = JSON.parse(res);
          let token = infomation.token;
          request.get(`user/referral/get?page=${page}&limit=${limit}`, token, [(data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          }]);
        })
        .catch((error) => reject(error));
    });
  }
export function GetReferalCountdown(): Promise<any> {
    return new Promise((resolve, reject) => {
      AsyncStorage.getItem("cookies")
        .then((res) => {
          let infomation = JSON.parse(res);
          let token = infomation.token;
          request.get(`user/referral/count-down`, token, [(data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          }]);
        })
        .catch((error) => reject(error));
    });
  }
