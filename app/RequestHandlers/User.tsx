import AsyncStorage from "@react-native-async-storage/async-storage";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./Request";

const request = new RequestHandler();

export function GetUserDetails(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`user/user-details`, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function UpdateprofilePic(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.patch(`user/update-profile-pic`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function UpdateUser(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.patch(`user/update-user`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function UpdatePassword(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.patch(`user/change-password`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function ValidatePin(body: Object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`user/validate-pin`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function GetUserByName(username: string): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`user/get-user?value=${username}`, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function ResetPin(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`user/forget-pin`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function AddBeneficiary(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`user/beneficiary/add`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function EditBeneficiary(id, body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.put(`user/beneficiary/edit/${id}`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function GetBeneficiaries(
  page: number,
  limit: number,
  type: string
): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(
          `user/beneficiary/get?page=${page}&limit=${limit}&type=${type}`,
          token,
          [(data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          }]
        );
      })
      .catch((error) => reject(error));
  });
}
export function GetBeneByID(id): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`user/beneficiary/get/${id}`, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function DeleteBene(id): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.delete(`user/beneficiary/delete/${id}`, {}, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function VerifyBvn(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`kyc/verify-bvn`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function VerifyDoc(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`kyc/verify-docs`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function AddPushToken(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`notification/push-token/add`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function GetReferals(page, limit): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`user/referral/get?page=${page}&limit=${limit}`, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function GetSfxPoint(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`user/reward/total-points`, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function ClaimSfxPoint(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`user/reward/claim`, {}, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function Disable2fa(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`user/2fa/disable`, {}, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function Get2FaToken(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`user/2fa/get-token`, {}, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function Enable2Fa(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`user/2fa/enable`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function Verify2FaToken(body: object, tkn?: string): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation ? infomation.token : tkn;
        request.post(`user/2fa/verify`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function VerifyBiomatric(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`user/biometrics/verify`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function AddAdditionalId(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`kyc/add-additional-id`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function CheckId(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`kyc/check-id`, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function CheckSession(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`user/check-session?token=${token}`, "", [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function GetBackupCode(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`user/2fa/get-backup-codes`, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function VerifyBackupCode(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`user/2fa/verify-backup-code`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function CheckReferralCode(referralCode: string): Promise<any> {
  return new Promise((resolve, reject) => {
    request.get(`user/check-user?referralCode=${referralCode}`, "", [(data, error) => {
      if (error) {
        reject(error);
      } else {
        resolve(data);
      }
    }]);
  });
}
export function CheckPhone(phone: string): Promise<any> {
  return new Promise((resolve, reject) => {
    request.get(`user/check-user?phone=${phone}`, "", [(data, error) => {
      if (error) {
        reject(error);
      } else {
        resolve(data);
      }
    }]);
  });
}
