// Usage:
// import { useToast } from '../context/ToastContext';
// const { handleToast } = useToast();
// await withApiErrorToast(GetUserDetails(), handleToast);

export async function withApiErrorToast<T>(
  promise: Promise<T>,
  handleToast: (msg: string, type?: "error" | "success" | "pending") => void
): Promise<T | undefined> {
  try {
    return await promise;
  } catch (error: any) {
    if (error?.code === 'TIMEOUT') {
      handleToast("Request timed out. Please try again.", "error");
    } else if (error?.code === 'NETWORK_ERROR') {
      handleToast("An error occurred. Please try again.", "error");
    } else {
      handleToast(error?.message || "An error occurred. Please try again.", "error");
    }
    return undefined;
  }
} 